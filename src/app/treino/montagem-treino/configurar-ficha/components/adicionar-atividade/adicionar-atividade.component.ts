import {
	Component,
	OnInit,
	ChangeDetectorRef,
	ViewChild,
	Input,
	Output,
	EventEmitter,
	ElementRef,
} from "@angular/core";
import {
	FormBuilder,
	FormGroup,
	FormArray,
	FormControl,
	Validators,
} from "@angular/forms";
import { ModalService } from "@base-core/modal/modal.service";
import { LocalizationService } from "@base-core/localization/localization.service";
import { WindowUtilService } from "@base-core/utils/window-util.service";
import { SnotifyService } from "ng-snotify";
import { SessionService } from "@base-core/client/session.service";
import {
	TipoAtividade,
	GrupoMuscular,
	AtividadeBase,
	Programa,
	PerfilAcessoRecursoNome,
	FichaPrograma,
} from "treino-api";
import {
	TreinoApiAtividadeService,
	TreinoApiGrupoMuscularService,
	TreinoApiProgramaService,
} from "treino-api";
import createNumberMask from "text-mask-addons/dist/createNumberMask";
import { RestService } from "@base-core/rest/rest.service";
import { ApiResponseList } from "@base-core/rest/rest.model";
import {
	buildAtividadeFichaForm,
	buildSerieAtividadeForm,
} from "../../ficha-form-group";
import { ConfigurarFichaService } from "../../../configurar-ficha.service";
import {
	CatFormInputComponent,
	SelectFilterParamBuilder,
	SelectFilterResponseParser,
	TraducoesXinglingComponent,
} from "ui-kit";
import { map, switchMap, take, tap } from "rxjs/operators";
import { of } from "rxjs";
import { Router } from "@angular/router";

enum TipoAtividadeControl {
	NEURO = "NEURO",
	CARDIO = "CARDIO",
	CARDIO_APENAS_DURACAO = "CARDIO_APENAS_DURACAO",
}

@Component({
	selector: "pacto-adicionar-atividade",
	templateUrl: "./adicionar-atividade.component.html",
	styleUrls: ["./adicionar-atividade.component.scss"],
})
export class AdicionarAtividadeComponent implements OnInit {
	@Input() fichaFormGroup;
	@Input() origemFichaPredefinida;
	@Output() validaFicha = new EventEmitter();
	@Output() addAtividade = new EventEmitter();

	@ViewChild("traducoes", { static: true })
	traducoes: TraducoesXinglingComponent;
	@ViewChild("atividadeTemplate", { static: true }) atividadeTemplate;
	@ViewChild("atividadeSelect", { static: true }) atividadeSelect;
	@ViewChild("inputSeries", { static: false })
	inputSeries: CatFormInputComponent;

	programa: Programa;
	tipo: TipoAtividadeControl = TipoAtividadeControl.NEURO;
	ficha: FichaPrograma;
	permissaoProgramaTreino;
	fichaSelecionada;
	fichaSelecionadaNome;
	primeiraFicha;
	primeiraAtividade;
	ultimaAtividade;
	index: number = 0;

	formGroupFichas: FormGroup;
	atividades: Array<AtividadeBase> = [];
	grupoMusculares: Array<any> = [];
	fichaSendoModificada = false;

	fichas: Array<FichaPrograma> = [];
	fichasPrograma: Array<any> = [];

	formGroup: FormGroup;
	map: { [key: string]: Array<string> } = {
		NEURO: ["carga", "cadencia", "repeticoes", "series", "descanso"],
		CARDIO: ["series", "velocidade", "duracao", "distancia", "descanso"],
		CARDIO_APENAS_DURACAO: ["series", "duracao", "descanso"],
	};

	mostrarContainerAtividades: boolean = false;
	colunasGrid: string = "col-md-12";
	colunasGridFluxo: boolean = false;
	mostrarColuna: boolean = false;
	mensagemSemAtividades: string;
	allGruposMusculares: Array<GrupoMuscular> = [];
	gruposMuscularesAtividadeSelecionada: Array<GrupoMuscular> = [];

	showGerarTreinoIA: boolean = false;

	constructor(
		private fb: FormBuilder,
		private configurarFichaService: ConfigurarFichaService,
		private atividadeService: TreinoApiAtividadeService,
		private programaService: TreinoApiProgramaService,
		private localizationService: LocalizationService,
		private grupoMuscularService: TreinoApiGrupoMuscularService,
		private utils: WindowUtilService,
		private snotify: SnotifyService,
		private cd: ChangeDetectorRef,
		private sessionService: SessionService,
		private rest: RestService,
		private appModal: ModalService,
		private elRef: ElementRef,
		private router: Router
	) {
		this.formGroupFichas = this.fb.group({
			ficha: [""],
			atividades: this.fb.array([]),
		});

		this.formGroup = this.fb.group({
			grupoMuscularId: [""],
			atividadeId: [null, [Validators.required]],
			carga: [""],
			repeticoes: [""],
			series: [""],
			descanso: [""],
			velocidade: [""],
			distancia: [""],
			duracao: [""],
			cadencia: [""],
		});
	}

	ngOnInit() {
		this.programaService.mostrarComparacaoCriarRenovar$.subscribe(
			(valueFluxo) => {
				this.colunasGridFluxo = valueFluxo;
				this.cd.detectChanges();
			}
		);
		this.carregarPermissoes();
		this.initArrayFichas();

		this.formGroup
			.get("atividadeId")
			.valueChanges.subscribe((value: AtividadeBase) => {
				this.itemSelectHandler(value);
			});

		this.configurarFichaService.fichaSendoModificada$.subscribe((value) => {
			this.fichaSendoModificada = value;
			this.validaFicha.emit(this.fichaSendoModificada);
			this.cd.detectChanges();
		});

		this.configurarFichaService.fichaSelecionadaIndex$.subscribe((value) => {
			if (value === null) {
				this.formGroup.get("grupoMuscularId").disable();
				this.formGroup.get("atividadeId").disable();
			} else {
				this.formGroup.get("grupoMuscularId").enable();
				this.formGroup.get("atividadeId").enable();
			}
		});

		this.ficha = this.configurarFichaService.obterFichaAtual();
		this.validarPermissoesAddAtividade();
		this.carregarTodosGruposMusculares();
	}

	carregarTodosGruposMusculares() {
		this.grupoMuscularService
			.obterTodosGruposMusculares()
			.subscribe((dados) => {
				this.allGruposMusculares = dados;
				this.cd.detectChanges();
			});
	}

	ngAfterViewInit() {
		if (this.primeiraFicha && this.primeiraFicha.id) {
			this.formGroupFichas.get("ficha").setValue(this.primeiraFicha.id);
			this.localizarFicha(this.primeiraFicha.id);
		} else {
			console.warn("Nenhuma ficha disponível");
		}
		this.cd.detectChanges();
	}

	private initArrayFichas(): void {
		this.configurarFichaService.programa$
			.pipe(
				tap((programa) => (this.programa = programa)),
				switchMap((programa) => {
					this.showGerarTreinoIA = programa.situacaoUltimoTreinoAluno;
					if (programa.alunoId && programa.id) {
						return this.programaService
							.obterIdProgramaAnterior(programa.alunoId, programa.id)
							.pipe(
								switchMap((result) => {
									if (result) {
										return this.programaService
											.obterProgramaCompleto(result)
											.pipe(
												tap(
													(completoPrograma) =>
														(this.programa = completoPrograma)
												)
											);
									} else {
										return of(programa);
									}
								})
							);
					} else {
						return of(programa);
					}
				})
			)
			.subscribe((programa) => this.atualizarFichas(programa));
	}

	private atualizarFichas(programa: Programa): void {
		this.fichas = programa.fichas || [];
		this.primeiraFicha = this.fichas.find((ficha) => ficha);

		if (this.primeiraFicha) {
			this.primeiraAtividade = this.primeiraFicha.atividades.find(
				(atividade) => atividade
			);
		}

		const fichaIds = new Set<number>();
		this.fichasPrograma = [];

		this.fichas.forEach((ficha) => {
			const fichaId = Number(ficha.id);
			if (!fichaIds.has(fichaId)) {
				fichaIds.add(fichaId);
				this.fichasPrograma.push({ id: ficha.id, label: ficha.nome });
			}
		});

		if (this.primeiraFicha) {
			this.formGroupFichas.get("ficha").setValue(this.primeiraFicha.id);
			this.localizarFicha(this.primeiraFicha.id);
		}
		this.cd.detectChanges();
	}

	private carregarPermissoes() {
		this.permissaoProgramaTreino = this.sessionService.recursos.get(
			PerfilAcessoRecursoNome.PROGRAMA_TREINO
		);
	}

	private validarPermissoesAddAtividade() {
		if (!this.permissaoProgramaTreino.editar) {
			this.formGroup.get("atividadeId").disable();
		}
	}

	get numeroInteiro() {
		return createNumberMask({ prefix: "", thousandsSeparatorSymbol: "" });
	}

	get inteiroDoisCaracteres() {
		return [/[0-9]/, /[0-9]/];
	}

	get acaoHabilitada() {
		return this.formGroup.get("atividadeId").value;
	}

	get camposAtivos(): Array<string> {
		return this.map[this.tipo];
	}

	get minuteSecondMask() {
		return this.localizationService.getMinuteSecondMask();
	}

	get _rest() {
		return this.rest;
	}

	hasImage(atividade) {
		return (
			atividade.images && atividade.images.length > 0 && atividade.images[0].uri
		);
	}

	get defaultImage() {
		return "assets/images/icon-peso.svg";
	}

	localizarFicha(fichaId: number): void {
		this.fichaSelecionada = this.fichas.find(
			(item) => Number(item.id) === Number(fichaId)
		);
		if (this.fichaSelecionada) {
			this.populateAtividades(this.fichaSelecionada.atividades);
			this.fichaSelecionadaNome = this.fichaSelecionada.nome;
			this.cd.detectChanges();

			if (
				!this.fichaSelecionada.atividades ||
				this.fichaSelecionada.atividades.length === 0
			) {
				this.mensagemSemAtividades = "Não há atividades nessa ficha";
			} else {
				this.mensagemSemAtividades = "";
			}
		}
	}

	populateAtividades(atividades: any[]) {
		const formArray = this.formGroupFichas.get("atividades") as FormArray;
		formArray.clear();
		atividades.forEach((atividade) => {
			formArray.push(this.createAtividadeGroup(atividade));
		});
	}

	createAtividadeGroup(atividade: any): FormGroup {
		const atividadeId =
			atividade && atividade.atividade ? atividade.atividade.id : "";
		const nome =
			atividade && atividade.atividade ? atividade.atividade.nome : "";
		const series = atividade && atividade.series ? atividade.series.length : 0;
		const repeticoes =
			atividade && atividade.series && atividade.series[0]
				? atividade.series[0].repeticoes
				: 0;
		const carga =
			atividade && atividade.series && atividade.series[0]
				? atividade.series[0].carga
				: 0;
		const cadencia =
			atividade && atividade.series && atividade.series[0]
				? atividade.series[0].cadencia
				: 0;
		const descanso =
			atividade && atividade.series && atividade.series[0]
				? atividade.series[0].descanso
				: "00:00";
		const metodo =
			atividade && atividade.series && atividade.series[0]
				? atividade.series[0].complemento
				: "Não atribuído";

		return this.fb.group({
			atividadeId: new FormControl({ value: atividadeId, disabled: true }),
			nome: new FormControl({ value: nome, disabled: true }),
			series: new FormControl({ value: series, disabled: true }),
			repeticoes: new FormControl({ value: repeticoes, disabled: true }),
			carga: new FormControl({ value: carga, disabled: true }),
			cadencia: new FormControl({ value: cadencia, disabled: true }),
			descanso: new FormControl({ value: descanso, disabled: true }),
			metodo: new FormControl({ value: metodo, disabled: true }),
		});
	}

	copiarAtividade(index: number) {
		const formArray = this.formGroupFichas.get("atividades") as FormArray;
		const atividade = formArray.at(index).value;

		this.formGroup.patchValue({
			atividadeId: { id: atividade.atividadeId, nome: atividade.nome },
			series: atividade.series,
			repeticoes: atividade.repeticoes,
			carga: atividade.carga,
			cadencia: atividade.cadencia,
			descanso: atividade.descanso,
			velocidade: atividade.velocidade || "",
			distancia: atividade.distancia || "",
			duracao: atividade.duracao || "",
		});
	}

	adicionarAtividades() {
		if (this.permissaoProgramaTreino.editar) {
			if (this.origemFichaPredefinida) {
				if (this.fichaFormGroup.get("nome").value.length > 2) {
					this.configurarFichaService.salvarFicha().subscribe((result) => {
						this.ficha = this.configurarFichaService.obterFichaAtual();
						this.cd.detectChanges();
					});
					this.addAtividade.emit();
				} else {
					this.snotify.warning("Preencha o nome da ficha para prosseguir.");
				}
			} else {
				this.addAtividade.emit();
			}
		}
	}

	itemSelectHandler(item: AtividadeBase) {
		if (item) {
			if (item.tipo === TipoAtividade.NEUROMUSCULAR) {
				this.tipo = TipoAtividadeControl.NEURO;
			} else if (item.serieApenasDuracao) {
				this.tipo = TipoAtividadeControl.CARDIO_APENAS_DURACAO;
			} else {
				this.tipo = TipoAtividadeControl.CARDIO;
			}
			if (this.inputSeries) {
				this.inputSeries.focus();
			}
			this.gruposMuscularesAtividadeSelecionada = item.gruposMusculares;
		}
	}

	responseParser: SelectFilterResponseParser = (
		response: ApiResponseList<any>
	) => {
		const result = response.content;
		result.forEach((atividade, index) => {
			atividade.index = index;
		});
		return result;
	};

	atividadeSelectBuilder: SelectFilterParamBuilder = (term) => {
		return {
			page: "0",
			size: "1000",
			filters: JSON.stringify({
				situacaoAtividade: ["ATIVO"],
				quicksearchValue: term,
				quicksearchFields: ["nome"],
				grupoMuscularesIds: this.grupoMusculares,
			}),
		};
	};

	adicionarHandler() {
		if (this.formGroup.valid) {
			const dto = this.getDto();
			const atividadeId = this.formGroup.get("atividadeId").value;
			this.configurarFichaService.adicionarAtividadeFicha(dto, atividadeId);
			this.salvarFichaHandler();
		}
	}

	private salvarFichaHandler() {
		if (this.fichaSendoModificada) {
			this.configurarFichaService.salvarFicha().subscribe(() => {
				this.gruposMuscularesAtividadeSelecionada = [];
				this.cd.detectChanges();
				this.snotify.success(this.traducoes.getLabel("ficha-save-success"));
			});
			this.formGroup.get("atividadeId").setValue(null);
		}
	}

	private getDto(): FormGroup {
		const formPage = this.formGroup.getRawValue();
		const atividadeFichaForm: FormGroup = buildAtividadeFichaForm();

		atividadeFichaForm.get("atividadeId").setValue(formPage.atividadeId.id);
		for (let i = 0; i < formPage.series; i++) {
			const serieAtividadeForm = buildSerieAtividadeForm();
			serieAtividadeForm.get("sequencia").setValue(i + 1);
			serieAtividadeForm
				.get("repeticoes")
				.setValue(formPage.repeticoes || null);
			serieAtividadeForm.get("carga").setValue(formPage.carga || null);
			serieAtividadeForm.get("cadencia").setValue(formPage.cadencia || null);
			serieAtividadeForm.get("distancia").setValue(formPage.distancia || null);
			serieAtividadeForm
				.get("descanso")
				.setValue(
					formPage.descanso
						? this.convertMinutesSecondsIntoSeconds(formPage.descanso)
						: null
				);
			serieAtividadeForm
				.get("duracao")
				.setValue(
					formPage.duracao
						? this.convertMinutesSecondsIntoSeconds(formPage.duracao)
						: null
				);
			serieAtividadeForm
				.get("velocidade")
				.setValue(formPage.velocidade || null);
			(atividadeFichaForm.get("series") as FormArray).push(serieAtividadeForm);
		}
		return atividadeFichaForm;
	}

	convertMinutesSecondsIntoSeconds(value: string): number {
		if (typeof value === "string") {
			const [minutes, seconds] = value.split(":");
			return parseInt(minutes, 10) * 60 + parseInt(seconds, 10);
		}
		return 0;
	}

	toggleContainerAtividades() {
		this.atividadeService.mostrarContainerAtividades$
			.pipe(take(1))
			.subscribe((valorAtual) => {
				const novoValor = !valorAtual;
				this.atividadeService.setMostrarContainerAtividades(novoValor);
			});

		this.mostrarContainerAtividades = !this.mostrarContainerAtividades;
		this.colunasGrid = this.mostrarContainerAtividades
			? "col-md-8"
			: "col-md-12";

		if (this.mostrarContainerAtividades) {
			this.mostrarColuna = true;
		} else {
			setTimeout(() => {
				this.mostrarColuna = false;
			}, 500);
		}
	}
}
