# Relatório: Implementação do Filtro de Aparelhos IA/Convencionais

## Visão Geral

Este relatório documenta a implementação do filtro de "Aparelhos IA" e "Aparelhos Convencionais" no componente `pacto-aparelho-lista`, replicando a funcionalidade existente no componente `pacto-atividade-lista`.

## Contexto

O sistema já possui filtros para atividades que permitem distinguir entre:
- **Atividades da IA**: Criadas automaticamente pela inteligência artificial
- **Atividades Convencionais**: Criadas manualmente pelos usuários

Foi solicitado replicar essa funcionalidade para aparelhos, permitindo filtrar entre "Aparelhos da IA" e "Aparelhos Convencionais".

## Implementações Realizadas no Frontend

### 1. Modelos TypeScript Atualizados

#### Campos IA Adicionados
```typescript
// Interface Aparelho (aparelho.model.ts)
export interface Aparelho {
    // ... campos existentes
    idEquipment_Ia?: number;
    nameEquipment_Ia?: string;
    isRequired_Ia?: boolean;
}

// Interface AparelhoEdit (aparelho.model.ts)
export interface AparelhoEdit {
    // ... campos existentes
    idEquipment_Ia?: number;
    nameEquipment_Ia?: string;
    isRequired_Ia?: boolean;
}

// Interface AparelhoBase (wod.model.ts)
export interface AparelhoBase {
    // ... campos existentes
    idEquipment_Ia?: number;
    nameEquipment_Ia?: string;
    isRequired_Ia?: boolean;
}

// Interface FiltroAparelho (aparelho.model.ts)
export interface FiltroAparelho {
    // ... campos existentes
    aparelhos?: string[]; // Filtro IA/Convencional
}
```

### 2. Componente aparelho-lista.component.ts

#### Filtro Adicionado
```typescript
private addFilter() {
    const result = [];
    // ... filtros existentes
    
    // NOVO FILTRO ADICIONADO
    result.push({
        name: "aparelhos",
        label: "Aparelhos",
        type: GridFilterType.MANY,
        showInputType: true,
        options: [
            { value: "IA", label: "Aparelhos da IA" },
            { value: "CONVENCIONAL", label: "Aparelhos Convencionais" },
        ],
    });
    
    // ... outros filtros
    return result;
}
```

#### Processamento do Filtro
```typescript
filterHandler(filter) {
    // ... código existente
    
    const filtros = {
        quicksearchValue: this.searchControl.value,
        situacaoAparelho: this.temporaryFilters.filters.situacaoAparelho.length > 0
            ? this.temporaryFilters.filters.situacaoAparelho
            : ["true"],
        aparelhos: this.temporaryFilters.filters.aparelhos, // NOVO CAMPO
        ordenacao: this.temporaryFilters.filters.ordenacao.length === 1
            ? this.temporaryFilters.filters.ordenacao[0]
            : "asc",
        quicksearchFields: ["nome", "sigla"],
    };
    
    // ... resto do código
}
```

#### Filtro Inicial
```typescript
// Método listarAparelhos()
this.filtro = {
    quicksearchValue: this.searchControl.value,
    quicksearchFields: ["nome"],
    ordenacao: "asc",
    situacaoAparelho: ["true"],
    aparelhos: [], // NOVO CAMPO INICIALIZADO
};
```

### 3. Funcionalidade Implementada

- ✅ Filtro "Aparelhos" com duas opções
- ✅ "Aparelhos da IA" - para aparelhos criados pela IA
- ✅ "Aparelhos Convencionais" - para aparelhos criados manualmente
- ✅ Integração com sistema de filtros existente
- ✅ Compatibilidade com modelos TypeScript
- ✅ Padrão consistente com filtro de atividades

## Requisitos para o Backend

### 1. Campos de Banco de Dados

O backend deve garantir que a tabela de aparelhos contenha os campos IA:

```sql
-- Campos que devem existir na tabela de aparelhos
idEquipment_Ia INTEGER,
nameEquipment_Ia VARCHAR(255),
isRequired_Ia BOOLEAN DEFAULT FALSE
```

### 2. Endpoint de Listagem com Filtros

O endpoint `GET /psec/aparelhos` deve processar o novo filtro `aparelhos`:

```json
{
    "quicksearchValue": "string",
    "situacaoAparelho": ["true", "false"],
    "aparelhos": ["IA", "CONVENCIONAL"],
    "ordenacao": "asc",
    "quicksearchFields": ["nome", "sigla"]
}
```

### 3. Lógica de Filtro

O backend deve implementar a seguinte lógica:

```java
// Pseudocódigo para o filtro
if (filtro.aparelhos != null && !filtro.aparelhos.isEmpty()) {
    for (String tipoAparelho : filtro.aparelhos) {
        if ("IA".equals(tipoAparelho)) {
            // Filtrar aparelhos onde idEquipment_Ia IS NOT NULL
            query.where(aparelho.idEquipment_Ia.isNotNull());
        }
        if ("CONVENCIONAL".equals(tipoAparelho)) {
            // Filtrar aparelhos onde idEquipment_Ia IS NULL
            query.where(aparelho.idEquipment_Ia.isNull());
        }
    }
}
```

### 4. Resposta da API

O backend deve retornar os campos IA nos objetos de resposta:

```json
{
    "content": [
        {
            "id": "123",
            "nome": "Esteira Profissional",
            "sigla": "EST",
            "ativo": true,
            "idEquipment_Ia": 456,
            "nameEquipment_Ia": "Professional Treadmill",
            "isRequired_Ia": false,
            // ... outros campos
        }
    ],
    "totalElements": 100,
    "totalPages": 10
}
```

### 5. Endpoints Afetados

Os seguintes endpoints devem ser atualizados:

1. **GET /psec/aparelhos** - Listagem com filtros
2. **GET /psec/aparelhos/{id}** - Consulta individual
3. **POST /psec/aparelhos** - Criação (aceitar campos IA)
4. **PUT /psec/aparelhos/{id}** - Atualização (aceitar campos IA)

### 6. Compatibilidade

O backend deve manter compatibilidade com:
- Aparelhos existentes sem campos IA (considerados "CONVENCIONAL")
- Filtros existentes (situacaoAparelho, ordenacao, etc.)
- Estrutura de resposta atual

## Exemplo de Uso

### Requisição de Filtro
```http
POST /psec/aparelhos/filtros
Content-Type: application/json

{
    "page": 0,
    "size": 20,
    "filtros": {
        "quicksearchValue": "",
        "situacaoAparelho": ["true"],
        "aparelhos": ["IA"],
        "ordenacao": "asc"
    }
}
```

### Resposta Esperada
```json
{
    "content": [
        {
            "id": "123",
            "nome": "Leg Press IA",
            "sigla": "LPIA",
            "ativo": true,
            "idEquipment_Ia": 789,
            "nameEquipment_Ia": "AI Leg Press Machine",
            "isRequired_Ia": true
        }
    ],
    "totalElements": 15,
    "totalPages": 1
}
```

## Considerações Técnicas

### 1. Performance
- Indexar campos `idEquipment_Ia` para otimizar consultas
- Considerar cache para filtros frequentes

### 2. Migração de Dados
- Aparelhos existentes devem ter `idEquipment_Ia = NULL` (convencionais)
- Não quebrar funcionalidade existente

### 3. Validação
- Validar valores do filtro `aparelhos` (apenas "IA" e "CONVENCIONAL")
- Manter validações existentes

## Status da Implementação

- ✅ **Frontend**: Implementado e funcional
- ⏳ **Backend**: Pendente de implementação
- ⏳ **Testes**: Pendente após implementação do backend

## Próximos Passos

1. **Backend**: Implementar lógica de filtro conforme especificado
2. **Testes**: Validar integração frontend/backend
3. **Documentação**: Atualizar documentação da API
4. **Deploy**: Coordenar deploy do frontend e backend

---

**Data**: 2025-01-30  
**Responsável Frontend**: Implementado  
**Responsável Backend**: Pendente  
**Prioridade**: Média
