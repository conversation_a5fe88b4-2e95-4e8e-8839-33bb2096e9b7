# Recomendações de Performance para Backend - TW-2714

## Problema Identificado
O endpoint `atividades/montarTreino` está apresentando lentidão significativa na busca de atividades, causando uma experiência ruim para o usuário.

## Análise do Frontend
O problema no frontend foi identificado e corrigido:
- **Cache local ineficiente** que causava resultados parciais
- **Debounce muito alto** (500ms reduzido para 300ms)
- **Lógica de mesclagem de cache** que causava inconsistências

## Recomendações para o Backend

### 1. Verificar Índices de Banco de Dados
O endpoint recebe filtros com `quicksearchValue` e `quicksearchFields: ["nome"]`, indicando busca por nome da atividade.

**Verificar se existe índice na coluna `nome` da tabela de atividades:**
```sql
-- PostgreSQL
CREATE INDEX IF NOT EXISTS idx_atividade_nome ON atividades (nome);

-- MySQL
CREATE INDEX idx_atividade_nome ON atividades (nome);

-- Para buscas LIKE com wildcards, considerar índice de texto completo:
-- PostgreSQL
CREATE INDEX idx_atividade_nome_gin ON atividades USING gin(to_tsvector('portuguese', nome));

-- MySQL
CREATE FULLTEXT INDEX idx_atividade_nome_fulltext ON atividades (nome);
```

### 2. Otimizar Consultas SQL
Se a consulta atual usa `LIKE '%termo%'`, considerar:

**Problema comum:**
```sql
-- LENTO - não pode usar índice eficientemente
SELECT * FROM atividades WHERE nome LIKE '%apoio%';
```

**Soluções recomendadas:**
```sql
-- RÁPIDO - pode usar índice
SELECT * FROM atividades WHERE nome ILIKE 'apoio%';

-- Ou usar busca de texto completo (PostgreSQL)
SELECT * FROM atividades WHERE to_tsvector('portuguese', nome) @@ to_tsquery('portuguese', 'apoio');
```

### 3. Implementar Cache no Backend
Considerar implementar cache Redis ou similar para consultas frequentes:
```java
@Cacheable(value = "atividades", key = "#filtros.quicksearchValue")
public Page<Atividade> buscarAtividades(FiltroAtividade filtros, Pageable pageable) {
    // implementação da busca
}
```

### 4. Otimizar Paginação
- Verificar se a paginação está sendo aplicada corretamente
- Considerar usar `LIMIT` e `OFFSET` de forma eficiente
- Para grandes volumes, considerar cursor-based pagination

### 5. Análise de Performance
Executar `EXPLAIN ANALYZE` nas consultas para identificar gargalos:
```sql
EXPLAIN ANALYZE SELECT * FROM atividades 
WHERE situacao = 'ATIVO' 
AND nome ILIKE '%apoio%' 
ORDER BY nome 
LIMIT 50;
```

### 6. Monitoramento
Implementar logs de performance para identificar consultas lentas:
```java
@Slf4j
public class AtividadeService {
    public Page<Atividade> buscarAtividades(FiltroAtividade filtros, Pageable pageable) {
        long startTime = System.currentTimeMillis();
        try {
            // busca
            return resultado;
        } finally {
            long duration = System.currentTimeMillis() - startTime;
            if (duration > 1000) { // Log se demorar mais que 1s
                log.warn("Busca de atividades demorou {}ms para filtro: {}", duration, filtros);
            }
        }
    }
}
```

## Próximos Passos
1. Aplicar as correções do frontend (já implementadas)
2. Verificar índices no banco de dados
3. Analisar e otimizar consultas SQL
4. Implementar monitoramento de performance
5. Testar com dados reais

## Arquivos Modificados no Frontend
- `projects/ui/src/lib/components/cat-select-filter/cat-select-filter.component.ts`
- `projects/ui/src/lib/components/cat-multi-select-filter/cat-multi-select-filter.component.ts`
- `src/app/treino/montagem-treino/configurar-ficha/components/adicionar-atividade/adicionar-atividade.component.ts`
