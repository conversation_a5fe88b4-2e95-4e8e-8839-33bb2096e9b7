# Implementação do Filtro de Tamanho da Página

## Resumo
Este documento descreve as alterações necessárias para adicionar um filtro de tamanho da página no componente de listagem de aparelhos, permitindo que o usuário selecione quantos itens deseja visualizar por página através do sistema de filtros.

## Alterações Necessárias

### 1. Componente TypeScript (aparelho-lista.component.ts)

#### 1.1 Adicionar novo filtro no método `addFilter()`

Localizar o método `addFilter()` (linha 260) e adicionar o novo filtro após os filtros existentes:

```typescript
private addFilter() {
    const result = [];
    
    // Filtros existentes...
    result.push({
        name: "situacaoAparelho",
        label: "Status",
        type: GridFilterType.MANY,
        translator: this.situacaoTranslator,
        options: [{ value: "true" }, { value: "false" }],
        initialValue: ["true"],
    });
    
    result.push({
        name: "aparelhos",
        label: "Aparelhos",
        type: GridFilterType.MANY,
        showInputType: true,
        options: [
            { value: "IA", label: "Aparelhos da IA" },
            { value: "CONVENCIONAL", label: "Aparelhos Convencionais" },
        ],
    });
    
    result.push({
        name: "ordenacao",
        label: "Ordenação",
        type: GridFilterType.MANY,
        options: [
            { value: "asc", label: "A|Z" },
            { value: "desc", label: "Z|A" },
        ],
    });
    
    // NOVO FILTRO - Tamanho da Página
    result.push({
        name: "tamanhoPagina",
        label: "Itens por Página",
        type: GridFilterType.MANY,
        options: [
            { value: "5", label: "5 itens" },
            { value: "10", label: "10 itens" },
            { value: "25", label: "25 itens" },
            { value: "50", label: "50 itens" },
        ],
        initialValue: ["5"],
    });
    
    return result;
}
```

#### 1.2 Modificar o método `filterHandler()`

Atualizar o método `filterHandler()` (linha 364) para processar o novo filtro:

```typescript
filterHandler(filter) {
    this.temporaryFilters = filter;
    this.filterDropdown.close();

    const filtros = {
        quicksearchValue: this.searchControl.value,
        situacaoAparelho:
            this.temporaryFilters.filters.situacaoAparelho.length > 0
                ? this.temporaryFilters.filters.situacaoAparelho
                : ["true"],
        aparelhos: this.temporaryFilters.filters.aparelhos,
        ordenacao:
            this.temporaryFilters.filters.ordenacao.length === 1
                ? this.temporaryFilters.filters.ordenacao[0]
                : "asc",
        quicksearchFields: ["nome", "sigla"],
        // NOVO: Processar filtro de tamanho da página
        tamanhoPagina:
            this.temporaryFilters.filters.tamanhoPagina.length === 1
                ? this.temporaryFilters.filters.tamanhoPagina[0]
                : "5",
    };

    this.filtro = filtros;
    
    // NOVO: Aplicar o tamanho da página selecionado no filtro
    if (filtros.tamanhoPagina) {
        const novoTamanho = parseInt(filtros.tamanhoPagina, 10);
        this.pageSizeControl.setValue(novoTamanho);
        this.data.size = novoTamanho;
    }
    
    this.data.page = this.ngbPage - 1;

    this.entityService
        .obterTodosAparelhosComFiltros(this.data, this.filtro)
        .subscribe((data) => {
            this.listaAparelhos = data.content || [];
            this.data.content = data.content ? data.content.length : 0;
            this.data.totalElements = data.totalElements || 0;
            this.cd.detectChanges();
            this.salvaEstado();
        });
    this.filtroButton.close();
}
```

#### 1.3 Corrigir método `listarAparelhos()`

O método `listarAparelhos()` precisa ser atualizado para incluir o `tamanhoPagina` no objeto filtro enviado ao backend:

```typescript
listarAparelhos() {
    this.data.size = this.pageSizeControl.value;
    this.data.page = this.ngbPage - 1;

    if (this.filtro !== undefined) {
        this.filtro.quicksearchValue = this.searchControl.value;
        // NOVO: Garantir que tamanhoPagina esteja sempre presente
        if (!this.filtro.tamanhoPagina) {
            this.filtro.tamanhoPagina = this.pageSizeControl.value.toString();
        }
    } else {
        this.filtro = {
            quicksearchValue: this.searchControl.value,
            quicksearchFields: ["nome"],
            ordenacao: "asc",
            situacaoAparelho: ["true"],
            aparelhos: [],
            // NOVO: Incluir tamanhoPagina na inicialização
            tamanhoPagina: this.pageSizeControl.value.toString(),
        };
    }

    this.entityService
        .obterTodosAparelhosComFiltros(this.data, this.filtro)
        .subscribe({
            // ... resto do código
        });
}
```

#### 1.4 Atualizar métodos de estado

Modificar o método `salvaEstado()` para incluir o filtro de tamanho:

```typescript
private salvaEstado() {
    const estado = {
        filtros: this.temporaryFilters,
        busca: this.searchControl.value,
        pagina: this.ngbPage,
        tamanhoPagina: this.pageSizeControl.value,
        // NOVO: Salvar filtro de tamanho da página
        filtroTamanhoPagina: this.temporaryFilters?.filters?.tamanhoPagina || ["5"],
    };
    localStorage.setItem(STORAGE_KEY, JSON.stringify(estado));
}
```

Modificar o método `restauraEstado()` para restaurar o filtro:

```typescript
private restauraEstado(estado) {
    if (estado.filtros) {
        this.temporaryFilters = estado.filtros;
        this.filtro = { ...this.baseFilter, ...estado.filtros };
    }
    if (estado.busca) {
        this.searchControl.setValue(estado.busca);
    }
    if (estado.pagina) {
        this.ngbPage = estado.pagina;
    }
    if (estado.tamanhoPagina) {
        this.pageSizeControl.setValue(estado.tamanhoPagina);
        this.data.size = estado.tamanhoPagina;
    }
    // NOVO: Restaurar filtro de tamanho da página
    if (estado.filtroTamanhoPagina && this.temporaryFilters) {
        if (!this.temporaryFilters.filters) {
            this.temporaryFilters.filters = {};
        }
        this.temporaryFilters.filters.tamanhoPagina = estado.filtroTamanhoPagina;
    }
}
```

### 2. Alterações no Backend (Recomendações)

#### 2.1 Endpoint de Filtros
O backend já deve estar preparado para receber o parâmetro `size` através do objeto `data` que é enviado na requisição. Verificar se o endpoint `obterTodosAparelhosComFiltros` está processando corretamente:

- `data.size`: Tamanho da página
- `data.page`: Número da página (0-based)

#### 2.2 Validação no Backend
Adicionar validação no backend para garantir que o `size` esteja dentro dos limites permitidos:

```java
// Exemplo em Java/Spring Boot
@GetMapping("/aparelhos")
public ResponseEntity<Page<Aparelho>> obterTodosAparelhosComFiltros(
    @RequestParam(defaultValue = "0") int page,
    @RequestParam(defaultValue = "5") int size,
    @RequestParam Map<String, Object> filtros) {
    
    // Validar tamanho da página
    if (size < 1 || size > 100) {
        size = 5; // Valor padrão
    }
    
    Pageable pageable = PageRequest.of(page, size);
    // ... resto da implementação
}
```

## Problema Identificado e Solução

### ❌ Problema Original
O filtro `tamanhoPagina` não estava sendo enviado para o backend porque:
1. O método `listarAparelhos()` não incluía o campo na inicialização do objeto `filtro`
2. Quando `this.filtro` era `undefined`, o objeto era criado sem o campo `tamanhoPagina`
3. Mesmo quando o filtro existia, o campo não era atualizado com o valor atual

### ✅ Solução Implementada
1. **Inicialização**: Incluído `tamanhoPagina` na criação inicial do objeto `filtro`
2. **Atualização**: Garantido que o campo seja sempre atualizado com o valor atual do `pageSizeControl`
3. **Sincronização**: O valor é convertido para string para manter consistência com outros filtros

### 🔧 Correção Adicional - Select não atualizava o filtro
**Problema**: O select `pacto-cat-select` chamava `listarAparelhos()` mas o filtro não era atualizado com o novo valor.

**Solução**: Modificado o método para sempre atualizar o `tamanhoPagina`:
```typescript
// ANTES (não atualizava se já existisse)
if (!this.filtro.tamanhoPagina) {
    this.filtro.tamanhoPagina = this.pageSizeControl.value.toString();
}

// DEPOIS (sempre atualiza)
this.filtro.tamanhoPagina = this.pageSizeControl.value.toString();
```

### 🚨 Correção Crítica - NumberFormatException
**Problema**: Backend recebia string vazia causando `NumberFormatException: For input string: ""`

**Causa**:
- `itensPerPage` tinha valores numéricos mas o select esperava strings
- Falta de validação para valores vazios

**Solução**:
1. **Alterado `itensPerPage` para usar strings**:
```typescript
// ANTES
itensPerPage = [
    { value: 5, label: "5" },
    { value: 10, label: "10" },
    // ...
];

// DEPOIS
itensPerPage = [
    { value: "5", label: "5" },
    { value: "10", label: "10" },
    // ...
];
```

2. **Adicionada validação no `listarAparelhos()`**:
```typescript
const pageSizeValue = this.pageSizeControl.value;
const pageSize = pageSizeValue && pageSizeValue !== "" ? parseInt(pageSizeValue, 10) : 5;
this.data.size = pageSize;
```

3. **Inicialização com string**:
```typescript
pageSizeControl = new FormControl("5");
```

## Benefícios da Implementação

1. **Consistência**: O filtro de tamanho da página fica integrado com os outros filtros
2. **Persistência**: O estado do filtro é salvo e restaurado entre sessões
3. **Sincronização**: O filtro fica sincronizado com o controle de tamanho existente
4. **UX Melhorada**: Usuário pode alterar o tamanho da página através do painel de filtros

## Considerações Técnicas

1. **Compatibilidade**: As alterações são compatíveis com Angular 8 e RxJS 6.4.0
2. **Performance**: O filtro não impacta a performance, apenas reorganiza a interface
3. **Manutenibilidade**: Segue o padrão existente de filtros no componente
4. **Testabilidade**: Pode ser testado junto com os outros filtros existentes

## Status da Implementação

✅ **IMPLEMENTADO**: As alterações foram aplicadas no componente `aparelho-lista.component.ts`:

1. ✅ Adicionado filtro "Itens por Página" no método `addFilter()`
2. ✅ Modificado método `filterHandler()` para processar o novo filtro
3. ✅ Atualizado método `salvaEstado()` para persistir o filtro
4. ✅ Modificado método `restauraEstado()` para restaurar o filtro
5. ✅ **CORRIGIDO**: Método `listarAparelhos()` agora inclui `tamanhoPagina` no objeto filtro enviado ao backend

## Arquivos Modificados

- `src/app/base/aparelho/components/aparelho-lista/aparelho-lista.component.ts`

## Próximos Passos

1. ✅ ~~Implementar as alterações no frontend conforme descrito~~
2. 🔄 Verificar se o backend já suporta os parâmetros necessários
3. 🔄 Testar a funcionalidade em ambiente de desenvolvimento
4. 🔄 Validar a persistência do estado entre sessões
5. 🔄 Verificar a sincronização com o controle de tamanho existente

## Como Testar

1. Abrir a página de listagem de aparelhos
2. Clicar no botão de filtros
3. Verificar se aparece a opção "Itens por Página"
4. Selecionar um tamanho diferente (ex: 10 itens)
5. Aplicar o filtro e verificar se a paginação muda
6. Recarregar a página e verificar se o filtro persiste
