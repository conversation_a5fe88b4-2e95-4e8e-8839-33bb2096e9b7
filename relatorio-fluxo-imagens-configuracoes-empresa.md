# Relatório: Fluxo de Imagens - Configurações de Empresa

## Resumo Executivo

Este relatório mapeia o fluxo completo de como as imagens são buscadas, processadas e salvas no sistema através do método `this.configTreinoService.getConfiguracoesEmpresa()`, incluindo todos os vínculos e dependências.

## 1. Ponto de Entrada - getConfiguracoesEmpresa()

### 1.1 Localização do Método
- **Arquivo**: `projects/treino-api/src/lib/treino-api-configuracoes-treino.service.ts`
- **Linha**: 323-332

<augment_code_snippet path="projects/treino-api/src/lib/treino-api-configuracoes-treino.service.ts" mode="EXCERPT">
````typescript
getConfiguracoesEmpresa(): Observable<ConfiguracoesEmpresa> {
    return this.restService
        .get("empresas/empdto/1")
        .pipe(
            map(
                (response: ApiResponseSingle<ConfiguracoesEmpresa>) =>
                    response.content
            )
        );
}
````
</augment_code_snippet>

### 1.2 Endpoint Backend
- **URL**: `empresas/empdto/1`
- **Método**: GET
- **Retorna**: `ConfiguracoesEmpresa` contendo dados da empresa incluindo `keyImgEmpresa`

## 2. Modelo de Dados - ConfiguracoesEmpresa

### 2.1 Estrutura do Modelo
- **Arquivo**: `projects/treino-api/src/lib/configuracoes-treino.model.ts`
- **Linhas**: 182-187

<augment_code_snippet path="projects/treino-api/src/lib/configuracoes-treino.model.ts" mode="EXCERPT">
````typescript
export class ConfiguracoesEmpresa {
    codigo?: any;
    nome?: any;
    timeZoneDefault?: any;
    keyImgEmpresa?: any;
}
````
</augment_code_snippet>

### 2.2 Campo de Imagem
- **Campo**: `keyImgEmpresa`
- **Tipo**: `any`
- **Função**: Armazena a chave/identificador da imagem da empresa

## 3. Componente de Configuração de Empresa

### 3.1 Componente Principal
- **Arquivo**: `src/app/base/configuracoes/components/configuracoes-empresa/configuracoes-empresa.component.ts`
- **Responsabilidade**: Interface para edição das configurações da empresa

### 3.2 FormGroup
<augment_code_snippet path="src/app/base/configuracoes/components/configuracoes-empresa/configuracoes-empresa.component.ts" mode="EXCERPT">
````typescript
formGroup: FormGroup = new FormGroup({
    codigo: new FormControl(null),
    nome: new FormControl(null),
    timeZoneDefault: new FormControl(null),
    keyImgEmpresa: new FormControl(null),
});
````
</augment_code_snippet>

### 3.3 Seletor de Imagem
- **Componente**: `SeletorImagemComponent`
- **Template**: `configuracoes-empresa.component.html`

<augment_code_snippet path="src/app/base/configuracoes/components/configuracoes-empresa/configuracoes-empresa.component.html" mode="EXCERPT">
````html
<pacto-seletor-imagem
    #seletorLogoEmpresa
    (cleared)="clearedImageHandler('keyImgEmpresa')"
    [control]="formGroup.get('keyImgEmpresa')"
    [height]="220"
    [nome]="'keyImgEmpresa'"
    [width]="180"></pacto-seletor-imagem>
````
</augment_code_snippet>

## 4. Processamento de Upload de Imagens

### 4.1 Componente SeletorImagemComponent
- **Arquivo**: `projects/old-ui-kit/src/lib/seletor-imagem/seletor-imagem.component.ts`
- **Função**: Gerencia upload e processamento de imagens

### 4.2 Processamento de Arquivo
<augment_code_snippet path="projects/old-ui-kit/src/lib/seletor-imagem/seletor-imagem.component.ts" mode="EXCERPT">
````typescript
handleFiles(event) {
    if (event.target.files && event.target.files[0]) {
        if (event.target.files[0].size < this.megaBytes) {
            this.verificaTipoImagem(event.target.files[0].type);
            if (this.tipo === "image") {
                const reader = new FileReader();
                reader.onload = (event1: any) => {
                    this.url = (<FileReader>event1.target).result.toString();
                    const tempImgData = (<FileReader>event1.target).result.toString();
                    this.imagemData = tempImgData.replace(
                        /^data:image\/\w+;base64,/,
                        ""
                    );
                    if (this.control) {
                        this.control.setValue(this.imagemData);
                    }
                    this.cd.detectChanges();
                };
                reader.readAsDataURL(event.target.files[0]);
            }
        }
    }
}
````
</augment_code_snippet>

### 4.3 Validação de Tamanho
- **Limite**: 16MB (16252928 bytes)
- **Validação adicional**: 2MB para configurações de empresa

## 5. Salvamento das Configurações

### 5.1 Método de Salvamento
<augment_code_snippet path="src/app/base/configuracoes/components/configuracoes-empresa/configuracoes-empresa.component.ts" mode="EXCERPT">
````typescript
saveHandler() {
    const dto = this.formGroup.getRawValue();
    
    if (dto.keyImgEmpresa == null || dto.keyImgEmpresa === "") {
        this.saveConfig();
    } else {
        // Validação defensiva para verificar se há arquivo selecionado e seu tamanho
        const inputElement = this.seletorLogoEmpresa.inputImagem.nativeElement;
        const files = inputElement.files;
        const maxSizeInBytes = 2097152; // 2MB em bytes
        
        if (files && files.length > 0 && files[0].size > maxSizeInBytes) {
            const configError = this.configError.nativeElement.innerHTML;
            this.notify.error(configError);
        } else {
            this.saveConfig();
        }
    }
}
````
</augment_code_snippet>

### 5.2 Método saveConfig()
<augment_code_snippet path="src/app/base/configuracoes/components/configuracoes-empresa/configuracoes-empresa.component.ts" mode="EXCERPT">
````typescript
private saveConfig() {
    const dto = this.formGroup.getRawValue();
    const save$ = this.configTreinoService.updateConfiguracoesEmpresa(dto);
    const update$ = this.configCache.loadTreinoConfigCache();
    return save$.pipe(switchMap(() => update$)).subscribe(() => {
        const configSuccess = this.configSuccess.nativeElement.innerHTML;
        this.notify.success(configSuccess);
    });
}
````
</augment_code_snippet>

### 5.3 Endpoint de Atualização
- **Método**: `updateConfiguracoesEmpresa()`
- **URL**: `empresas/alterarEmpresa`
- **Método HTTP**: POST

## 6. Cache de Configurações

### 6.1 Serviço de Cache
- **Arquivo**: `src/app/base/configuracoes/configuration.service.ts`
- **Classe**: `TreinoConfigCacheService`

### 6.2 Carregamento do Cache
<augment_code_snippet path="src/app/base/configuracoes/configuration.service.ts" mode="EXCERPT">
````typescript
loadTreinoConfigCache(): Observable<any> {
    if (!this.sessionService.isModuloHabilitado(PlataformaModulo["ZW"])) {
        return zip(
            this.configTreinoService.getConfiguracoesAula(),
            this.configTreinoService.getConfiguracoesAplicativo(),
            this.configTreinoService.getConfiguracoesGestao(),
            this.configTreinoService.getConfiguracoesTreino(),
            this.configTreinoService.getConfiguracoesAvaliacao(),
            this.configTreinoService.getConfiguracoesIntegracoes(),
            this.configTreinoService.getConfiguracoesEmpresa(),
            this.configTreinoService.getConfiguracoesNotificacao(),
            this.configTreinoService.getConfiguracoesIntegracoesListaMQV(),
            this.configTreinoService.getConfiguracoesManutencao(),
            this.configTreinoService.getConfiguracoesIa()
        ).pipe(
            tap((result) => {
                this.configuracoesAula = result[0];
                this.configuracoesAplicativo = result[1];
                this.configuracoesGestao = result[2];
                this.configuracoesTreino = result[3];
                this.configuracoesAvaliacao = result[4];
                this.configuracoesIntegracoes = result[5];
                this.configuracoesEmpresa = result[6];
                this.configuracoesNotificacao = result[7];
                this.configuracoesIntegracoesListaMQV = result[8];
                this.configuracoesManutencao = result[9];
                this.configuracoesIa = result[10];
            })
        );
    }
}
````
</augment_code_snippet>

## 7. Utilização da Imagem no Sistema

### 7.1 Configuração do Menu da Plataforma
- **Arquivo**: `src/app/base/plataform-layout/plataforma-v2-config.service.ts`

<augment_code_snippet path="src/app/base/plataform-layout/plataforma-v2-config.service.ts" mode="EXCERPT">
````typescript
avatarRedeUrl:
    this.sessionService.currentEmpresa &&
    this.sessionService.currentEmpresa.fotoKey !== ""
        ? this.sessionService.currentEmpresa.fotoKey
        : "./assets/images/empty-image.png",
````
</augment_code_snippet>

### 7.2 Wrapper do SDK de Treino
- **Arquivo**: `src/app/services/treino-sdk-wrapper.service.ts`

<augment_code_snippet path="src/app/services/treino-sdk-wrapper.service.ts" mode="EXCERPT">
````typescript
avatarRedeUrl:
    this._sessionService.currentEmpresa &&
    this._sessionService.currentEmpresa.fotoKey !== ""
        ? this._sessionService.currentEmpresa.fotoKey
        : "./assets/images/empty-image.png",
````
</augment_code_snippet>

## 8. Fluxo de Dados Completo

### 8.1 Busca de Dados
1. **Chamada**: `configTreinoService.getConfiguracoesEmpresa()`
2. **Endpoint**: `GET empresas/empdto/1`
3. **Retorno**: Objeto `ConfiguracoesEmpresa` com `keyImgEmpresa`
4. **Cache**: Armazenado em `TreinoConfigCacheService.configuracoesEmpresa`

### 8.2 Processamento de Upload
1. **Componente**: `SeletorImagemComponent`
2. **Conversão**: Arquivo → Base64
3. **Validação**: Tamanho (2MB) e formato (image/*)
4. **Armazenamento**: FormControl `keyImgEmpresa`

### 8.3 Salvamento
1. **Validação**: Tamanho do arquivo
2. **Endpoint**: `POST empresas/alterarEmpresa`
3. **Payload**: DTO com `keyImgEmpresa` em Base64
4. **Cache**: Atualização automática via `loadTreinoConfigCache()`

### 8.4 Exibição
1. **Fonte**: `sessionService.currentEmpresa.fotoKey`
2. **Fallback**: `./assets/images/empty-image.png`
3. **Contextos**: Menu da plataforma, configurações, etc.

## 9. Considerações Técnicas

### 9.1 Formatos Suportados
- **Tipos**: JPEG, JPG, PNG
- **Tamanho máximo**: 2MB para configurações de empresa
- **Codificação**: Base64

### 9.2 Validações
- Verificação de tipo de arquivo
- Validação de tamanho
- Tratamento de erros

### 9.3 Performance
- Cache de configurações para evitar múltiplas requisições
- Compressão automática quando necessário
- Fallback para imagem padrão

## 10. Dependências e Integrações

### 10.1 Serviços Relacionados
- `TreinoApiConfiguracoesTreinoService`
- `TreinoConfigCacheService`
- `SessionService`
- `SnotifyService`

### 10.2 Componentes Relacionados
- `SeletorImagemComponent`
- `ConfiguracoesEmpresaComponent`
- `PlataformaV2ConfigService`

### 10.3 APIs Backend
- `empresas/empdto/1` (GET)
- `empresas/alterarEmpresa` (POST)

## Conclusão

O fluxo de imagens no sistema de configurações de empresa é bem estruturado, com separação clara de responsabilidades entre busca, processamento, validação e salvamento. O sistema utiliza cache para otimizar performance e fornece fallbacks adequados para casos onde a imagem não está disponível.
