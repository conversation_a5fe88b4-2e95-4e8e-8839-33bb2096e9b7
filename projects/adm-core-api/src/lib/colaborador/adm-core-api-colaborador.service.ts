import { Injectable } from "@angular/core";
import { AdmCoreApiModule } from "../adm-core-api.module";
import { AdmCoreApiBaseService } from "../adm-core-api-base.service";
import { ApiResponseList, ApiResponseSingle, DataFiltro } from "../base.model";
import { Colaborador } from "../contrato/contrato-modalidade.model";
import { Observable } from "rxjs";
import { map } from "rxjs/operators";

@Injectable({
	providedIn: AdmCoreApiModule,
})
export class AdmCoreApiColaboradorService {
	constructor(private admCoreApiBaseService: AdmCoreApiBaseService) {}

	findAllConsultoresAtivos(): Observable<ApiResponseList<Colaborador>> {
		return this.admCoreApiBaseService.get<ApiResponseList<Colaborador>>(
			"colaboradores/consultores-ativos"
		);
	}

	findTipoColaborador(colaborador, usuario): Observable<any> {
		const params = { usuario };
		return this.admCoreApiBaseService
			.get(`colaboradores/${colaborador}/tipo`, { params })
			.pipe(
				map((result: ApiResponseSingle<any>) => {
					return result.content;
				})
			);
	}

	findAllAtivosPorTipoVinculo(
		tipoVinculo: string,
		filtros: DataFiltro,
		IsFull?: boolean
	): Observable<ApiResponseList<Colaborador>> {
		const page = filtros.page ? filtros.page : 0;
		const size = filtros.size ? filtros.size : 10;
		delete filtros.page;
		delete filtros.size;
		const params: any = {
			page,
			size,
			sort: `${filtros.sortField},${filtros.sortDirection}`,
			filters: JSON.stringify(filtros.filters || {}),
		};
		return this.admCoreApiBaseService.get<ApiResponseList<Colaborador>>(
			`colaboradores/ativos/${tipoVinculo}${IsFull ? "/full" : ""}`,
			{ params }
		);
	}
}
