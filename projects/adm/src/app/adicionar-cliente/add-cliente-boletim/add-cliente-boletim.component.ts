import { Router } from "@angular/router";
import { takeUntil } from "rxjs/operators";
import { ModalAddFotoComponent } from "@adm/adicionar-cliente/modal-add-foto/modal-add-foto.component";
import {
	ChangeDetectorRef,
	Component,
	EventEmitter,
	Input,
	OnDestroy,
	OnInit,
	Output,
} from "@angular/core";
import {
	FormBuilder,
	FormControl,
	FormGroup,
	Validators,
} from "@angular/forms";
import {
	AdmCoreApiColaboradorService,
	AdmCoreApiEventoService,
	AdmCoreApiFreePassService,
	AdmCoreApiIntegracoesService,
	AdmCoreApiQuestionarioClienteService,
	ApiResponseSingle as AdmCoreApiResponseSingle,
	ApiResponseList,
	Colaborador,
	Evento,
	Produto,
	QuestionarioCliente,
	AdmCoreApiNegociacaoService,
} from "adm-core-api";
import { AdmLegadoAutorizarAcessoService } from "adm-legado-api";
import { Subject } from "rxjs";
import { MatDialog } from "@angular/material";
import { PermissaoService } from "pacto-layout";
import { AdicionarClienteService } from "@adm/adicionar-cliente/adicionar-cliente.service";
import { SnotifyService } from "ng-snotify";
import { PerfilRecursoPermissoTipo, SessionService } from "sdk";
import { LoaderService } from "ui-kit";
import { ModalService, PactoModalSize } from "@base-core/modal/modal.service";
import { ModalAddConfirmacaoComponent } from "@adm/adicionar-cliente/modal-add-confirmacao/modal-add-confirmacao.component";
import { ClientDiscoveryService } from "../../../../../../src/app/microservices/client-discovery/client-discovery.service";

@Component({
	selector: "adm-add-cliente-boletim",
	templateUrl: "./add-cliente-boletim.component.html",
	styleUrls: ["./add-cliente-boletim.component.scss"],
})
export class AddClienteBoletimComponent implements OnInit, OnDestroy {
	@Input() getDataCadastro: any;
	@Output() callchangeMode = new EventEmitter<string>();

	form: FormGroup = new FormGroup({
		consultor: new FormControl(null, Validators.required),
		data: new FormControl(null, Validators.required),
		evento: new FormControl(),
		freepass: new FormControl(),
		observacao: new FormControl(),
		questionarioClienteGroup: new FormGroup({}),
		tipoVinculo: new FormControl("TW"),
		colaborador: new FormControl(),
		tipoAcessoGymPass: new FormControl(),
		gymidGymPass: new FormControl(),
		produtoGymPass: new FormControl(),
	});
	consultorList = [];
	freepassList = [];
	wellhubList = [];
	eventoList = [];
	tipoVinculoList = [];
	colaboradorList = [];
	vinculosList = [];
	permissao2_65_data_boletim = false;
	permissao2_29_vinculo: any;
	permissao4_23_vendaAvulsa: any;
	private _destroy: Subject<void> = new Subject();
	questionarioCliente: QuestionarioCliente;
	groupNsPrefix = "groupNs";
	controlNsPrefix = "controlNs";
	groupMePrefix = "groupMe";
	controlMePrefix = "controlMe";
	groupSePrefix = "groupSe";
	controlSePrefix = "controlSe";
	groupSnPrefix = "groupSn";
	controlSnPrefix = "controlSn";
	groupTePrefix = "groupTe";
	controlTePrefix = "controlTe";
	bvObrigatorio: boolean = false;
	empresaUsaGympass: boolean = false;
	tiposTokenGympass = [
		{
			id: null,
			label: "-",
		},
		{
			id: 1,
			label: "Tipo 1",
		},
		{
			id: 2,
			label: "Tipo 2",
		},
		{
			id: 3,
			label: "Tipo 3",
		},
		{
			id: 4,
			label: "Tipo 4",
		},
		{
			id: 5,
			label: "Tipo 5",
		},
	];
	private dataHoje: Date = new Date();

	constructor(
		private router: Router,
		private fb: FormBuilder,
		private pactoModal: ModalService,
		private clientDiscoveryService: ClientDiscoveryService,
		private autorizarAcessoService: AdmLegadoAutorizarAcessoService,
		private colaboradorService: AdmCoreApiColaboradorService,
		private admCoreApiNegociacaoService: AdmCoreApiNegociacaoService,
		private readonly adicionarClienteService: AdicionarClienteService,
		private msIntegracoesService: AdmCoreApiIntegracoesService,
		private freepassService: AdmCoreApiFreePassService,
		private eventoService: AdmCoreApiEventoService,
		private cd: ChangeDetectorRef,
		private snotifyService: SnotifyService,
		private matDialog: MatDialog,
		private loaderService: LoaderService,
		private permissaoService: PermissaoService,
		public sessionService: SessionService,
		private questionarioClienteService: AdmCoreApiQuestionarioClienteService
	) {}

	ngOnInit() {
		if (!this.adicionarClienteService.clienteSelecionado) {
			this.adicionarClienteService.clienteSelecionado = {};
		}
		if (this.adicionarClienteService.clienteSelecionado.matricula) {
			this.loaderService.initForce();
			this.acessarTelaCliente(
				true,
				this.adicionarClienteService.clienteSelecionado.matricula
			);
			return;
		}
		this.adicionarClienteService.empresaSelecionada = Number(
			this.sessionService.empresaId
		);
		this.obterConsultores();
		this.obterProdutosFreepass();
		this.obterProdutosWellhub();
		this.consultarConfiguracaoGymPass();
		this.obterEventos();
		this.obterTipoVinculos();
		this.obterColaboradorPorTipo();
		this.permissao2_65_data_boletim =
			this.permissaoService.temPermissaoAdm("2.65");
		this.permissao2_29_vinculo =
			this.sessionService.perfilUsuarioAdm.perfilUsuario.recursos.find(
				(r) => r.referenciaRecurso === "2.29"
			);
		this.permissao4_23_vendaAvulsa =
			this.permissaoService.temPermissaoAdm("4.23");
		this.dataHoje.setHours(0, 0, 0, 0);
		this.form.get("data").setValue(this.dataHoje);

		this.form.get("data").valueChanges.subscribe((v) => {
			const data = this.form.get("data").value;
			if (data) {
				const dateInp = new Date(data);
				dateInp.setHours(0, 0, 0, 0);
				const today = new Date();
				today.setHours(0, 0, 0, 0);
				if (
					!this.permissao2_65_data_boletim &&
					today.getTime() !== dateInp.getTime()
				) {
					this.snotifyService.error(
						'Você não possui a permissão "2.65 - Alterar data do boletim de visita"'
					);
					this.form.get("data").setValue(this.dataHoje);
				}
			}
		});
		this.loadBvVisitante();
	}

	ngOnDestroy(): void {
		this._destroy.next();
		this._destroy.complete();
	}

	private patchFormValues(): void {
		this.adicionarClienteService.clienteSelecionado.foto =
			this.adicionarClienteService.boletim.foto;
		this.adicionarClienteService.clienteSelecionado.urlFoto =
			this.adicionarClienteService.boletim.urlFoto;
		this.vinculosList = this.adicionarClienteService.boletim.vinculosList;
		const data = this.adicionarClienteService.boletim.form;
		Object.keys(data).forEach((key) => {
			if (this.form.get(key)) {
				this.form.get(key).patchValue(data[key]);
			}
		});
	}

	private obterConsultores() {
		this.colaboradorService
			.findAllConsultoresAtivos()
			.pipe(takeUntil(this._destroy))
			.subscribe({
				next: (response: any) => {
					this.consultorList = response.content.map((consultor: any) => ({
						codigo: consultor.codigo,
						nome: consultor.pessoa.nome,
					}));
					this.cd.detectChanges();
				},
				error: (error) => {
					console.error("Erro ao obter consultores ativos", error);
				},
			});
	}

	private obterProdutosFreepass() {
		this.freepassService
			.obterProdutosAtivos()
			.pipe(takeUntil(this._destroy))
			.subscribe((response: ApiResponseList<Produto>) => {
				if (response.content) {
					this.freepassList = response.content.map((p) => {
						let descricao = `${p.descricao} - ${p.nrDiasVigencia} `;
						if (p.nrDiasVigencia === 1) {
							descricao += "dia";
						} else {
							descricao += "dias";
						}

						return {
							codigo: p.codigo,
							descricao,
						};
					});
				}
				this.freepassList.unshift({
					codigo: null,
					descricao: "-",
				});
			});
	}

	private obterProdutosWellhub() {
		this.adicionarClienteService
			.produtosWellhub()
			.pipe(takeUntil(this._destroy))
			.subscribe((response: ApiResponseList<any>) => {
				if (response.content) {
					this.wellhubList = response.content.map((p) => {
						let descricao = `${p.descricao} - ${p.nrDiasVigencia} `;
						if (p.nrDiasVigencia === 1) {
							descricao += "dia";
						} else if (p.nrDiasVigencia > 1) {
							descricao += "dias";
						} else {
							descricao = p.descricao;
						}

						return {
							codigo: p.codigo,
							descricao,
						};
					});
				}
				this.wellhubList.unshift({
					codigo: null,
					descricao: "-",
				});
			});
	}

	private obterEventos() {
		this.eventoService
			.obterEventosHoje()
			.pipe(takeUntil(this._destroy))
			.subscribe((response) => {
				this.eventoList = response.content;

				this.eventoList.unshift({
					codigo: null,
					descricao: "-",
				});
			});
	}

	private obterTipoVinculos() {
		this.tipoVinculoList = new Array<any>(
			{
				codigo: "OR",
				descricao: "Orientador",
			},
			{
				codigo: "PE",
				descricao: "Personal Externo",
			},
			{
				codigo: "PI",
				descricao: "Personal Interno",
			},
			{
				codigo: "PT",
				descricao: "Personal Trainer",
			},
			{
				codigo: "PR",
				descricao: "Professor",
			},
			{
				codigo: "TW",
				descricao: "Professor (TreinoWeb)",
			},
			{
				codigo: "TE",
				descricao: "Terceirizado",
			}
		);
	}

	obterColaboradorPorTipo() {
		this.colaboradorService
			.findAllAtivosPorTipoVinculo(
				this.form.get("tipoVinculo").value,
				{
					page: 0,
					size: 100,
					sortField: "codigo",
					sortDirection: "ASC",
					filters: {},
					configs: {},
				},
				true
			)
			.subscribe((v) => {
				this.colaboradorList = v.content.map((colaborador) => ({
					descricao: colaborador.pessoa.nome,
					codigo: colaborador.codigo,
				}));
			});
	}

	consultarConfiguracaoGymPass() {
		this.msIntegracoesService
			.configuracaoIntegracaoGympass(this.sessionService.empresaId)
			.subscribe(
				(config) => {
					this.empresaUsaGympass =
						config.codigoGympass && config.codigoGympass.length > 0;
					this.cd.detectChanges();
				},
				(error) => {
					console.error(error);
					this.snotifyService.error(error.error.meta.message);
				}
			);
	}

	get showGymPass(): boolean {
		return this.empresaUsaGympass;
	}

	get showProdutoGymPass(): boolean {
		return this.permissao4_23_vendaAvulsa;
	}

	changeAvatar() {
		const dialogRef = this.matDialog.open(ModalAddFotoComponent, {
			width: "600px",
			height: "auto",
			autoFocus: false,
		});
		dialogRef.componentInstance.update.subscribe((res) => {
			this.cd.detectChanges();
		});
	}

	validar(visitante) {
		if (this.form.invalid) {
			if (this.form.get("consultor").invalid) {
				this.snotifyService.error("Consultor não informado!");
				return;
			}
			if (this.form.get("data").invalid) {
				this.snotifyService.error("Data não informada!");
				return;
			}
		}

		if (!this.validaAndConvertQuestoesObrigatorias()) {
			return;
		}

		const respondeuTodasQuestoes = this.validaRespondeuTodasQuestoes();
		if (
			this.permissaoService.temConfiguracaoEmpresaAdm("bvObrigatorio") &&
			!respondeuTodasQuestoes.valid
		) {
			this.snotifyService.error(respondeuTodasQuestoes.message);
			return;
		}

		if (!respondeuTodasQuestoes.valid) {
			const dialogRef = this.pactoModal.open(
				"Confirmação",
				ModalAddConfirmacaoComponent,
				PactoModalSize.MEDIUM
			);
			dialogRef.componentInstance.dados = {
				msg: "As respostas estão incompletas, deseja continuar assim mesmo?",
			};
			dialogRef.componentInstance.update.subscribe((respModal) => {
				if (respModal === "SIM") {
					this.validarFreePassGymPass(visitante);
				}
			});
		} else {
			this.validarFreePassGymPass(visitante);
		}
	}

	validarFreePassGymPass(visitante) {
		let informouGymPass = false;
		if (
			this.form.get("tipoAcessoGymPass").value !== null ||
			this.form.get("gymidGymPass").value !== null
		) {
			if (this.form.get("tipoAcessoGymPass").value === null) {
				this.snotifyService.error(
					"As informações de Wellhub estão incompletas, selecione o tipo de acesso."
				);
				return;
			}
			if (this.form.get("gymidGymPass").value === null) {
				this.snotifyService.error(
					"As informações de Wellhub estão incompletas, informe o token do Wellhub."
				);
				return;
			}
			informouGymPass = true;
		}

		if (this.form.get("freepass").value !== null || informouGymPass) {
			if (this.form.get("freepass").value !== null && informouGymPass) {
				this.snotifyService.error(
					"Você selecionou um freepass e também informou dados de Wellhub, informe somente uma opção."
				);
				return;
			}

			let msg;
			if (informouGymPass) {
				if (
					this.form.get("produtoGymPass").value &&
					this.form.get("produtoGymPass").value > 0
				) {
					const prodGympass = this.wellhubList.find(
						(x) => x.codigo === this.form.get("produtoGymPass").value
					);
					msg =
						"Você está cadastrando o cliente de nome: " +
						(this.adicionarClienteService.clienteSelecionado &&
						this.adicionarClienteService.clienteSelecionado.nome
							? this.adicionarClienteService.clienteSelecionado.nome
							: "") +
						" com um Wellhub: " +
						prodGympass.descricao +
						". Deseja continuar?";
				} else {
					msg =
						"Você está cadastrando o cliente de nome: " +
						(this.adicionarClienteService.clienteSelecionado &&
						this.adicionarClienteService.clienteSelecionado.nome
							? this.adicionarClienteService.clienteSelecionado.nome
							: "") +
						" com um Wellhub. Deseja continuar?";
				}
			} else {
				const prodFressPass = this.freepassList.find(
					(x) => x.codigo === this.form.get("freepass").value
				);
				msg =
					"Você está cadastrando o cliente de nome: " +
					(this.adicionarClienteService.clienteSelecionado &&
					this.adicionarClienteService.clienteSelecionado.nome
						? this.adicionarClienteService.clienteSelecionado.nome
						: "") +
					" com um Freepass: " +
					prodFressPass.descricao +
					". Deseja continuar?";
			}

			const dialogRef = this.pactoModal.open(
				"Atenção",
				ModalAddConfirmacaoComponent,
				PactoModalSize.MEDIUM
			);
			dialogRef.componentInstance.dados = {
				msg,
			};
			dialogRef.componentInstance.update.subscribe((respModal) => {
				if (respModal === "SIM") {
					this.autorizarAcessoService
						.validarPermissaoUsuarioLogado(
							this.sessionService.chave,
							this.sessionService.codUsuarioZW,
							this.sessionService.empresaId,
							"PermissaoFreePass",
							"2.51 - Lançar Free Pass"
						)
						.subscribe(
							(response) => {
								// cadastrou freepass ou gympass então manda para a tela do cliente
								this.save(true);
							},
							(httpResponseError) => {
								this.snotifyService.error(httpResponseError.error.meta.message);
							}
						);
				}
			});
		} else {
			this.save(visitante);
		}
	}

	save(visitante) {
		this.loaderService.initForce();

		this.questionarioCliente.consultor = {
			codigo: this.form.get("consultor").value,
		} as Colaborador;
		this.questionarioCliente.data = this.form.get("data").value;
		if (this.form.get("evento").value !== null) {
			this.questionarioCliente.evento = {
				codigo: this.form.get("evento").value,
			} as Evento;
		}
		if (this.form.get("freepass").value !== null) {
			this.questionarioCliente.freepass = {
				codigo: this.form.get("freepass").value,
			} as Produto;
		}
		if (this.form.get("gymidGymPass").value !== null) {
			this.adicionarClienteService.clienteSelecionado.dadosGymPass = {
				tipo: this.form.get("tipoAcessoGymPass").value,
				gymId: this.form.get("gymidGymPass").value,
				produto: this.form.get("produtoGymPass").value,
			};
		}

		this.questionarioCliente.observacao = this.form.get("observacao").value;
		const respondeuTodasQuestoes = this.validaRespondeuTodasQuestoes();

		const body = {
			...this.adicionarClienteService.clienteSelecionado,
			vinculos: this.vinculosList,
			questionarioCliente: this.questionarioCliente,
			respondeuTodasQuestoes: respondeuTodasQuestoes.valid,
		};

		this.adicionarClienteService.gravar(body).subscribe(
			(resp: any) => {
				console.log(resp);
				if (visitante) {
					this.notificar("INCLUIR_CLIENTE_V2_CADASTROU_VISITANTE");
				} else {
					this.notificar("INCLUIR_CLIENTE_V2_CADASTROU_NEGOCIACAO");
				}
				this.adicionarClienteService.clienteSelecionado.matricula =
					resp.matricula;
				this.acessarTelaCliente(visitante, resp.matricula);
			},
			({ error = {} }) => {
				this.loaderService.stopForce();
				this.snotifyService.error(error.meta.message);
			}
		);
	}

	acessarTelaCliente(visitante, matricula) {
		if (visitante) {
			this.admCoreApiNegociacaoService
				.recursoHabilitado("TELA_ALUNO")
				.subscribe({
					next: (responseV) => {
						this.loaderService.stopForce();
						if (responseV) {
							this.router.navigateByUrl("/pessoas/perfil-v2/" + matricula);
						} else {
							this.acessarPerfilAlunoAntigo(matricula);
						}
					},
					error: (err) => {
						this.loaderService.stopForce();
						this.acessarPerfilAlunoAntigo(matricula);
					},
				});
		} else {
			this.loaderService.stopForce();
			this.router.navigateByUrl(
				"/adm/negociacao/contrato/" + matricula + "/novo"
			);
		}
	}

	acessarPerfilAlunoAntigo(matricula) {
		this.clientDiscoveryService
			.linkZw(this.sessionService.usuarioOamd, this.sessionService.empresaId)
			.subscribe((urlZw) => {
				const url = `${urlZw}&urlRedirect=uriCliente&matriculaCliente=${matricula}&menu=true`;
				window.open(url, "_self");
			});
	}

	voltar() {
		this.adicionarClienteService.boletim = {
			vinculosList: this.vinculosList,
			form: this.form.getRawValue(),
			foto: this.adicionarClienteService.clienteSelecionado.foto,
			urlFoto: this.adicionarClienteService.clienteSelecionado.urlFoto,
		};
		this.router.navigateByUrl("/adm/adicionar-cliente/cadastro");
	}

	private loadBvVisitante() {
		this.questionarioClienteService
			.obterBvVisitante()
			.subscribe((response: AdmCoreApiResponseSingle<QuestionarioCliente>) => {
				this.questionarioCliente = response.content;
				const dataHoje = new Date();
				dataHoje.setHours(0, 0, 0, 0);
				this.questionarioCliente.data = dataHoje;
				this.buildQuestionarioForm();
				if (this.adicionarClienteService.boletim) {
					this.patchFormValues();
				}
				this.cd.detectChanges();
			});
	}

	private buildQuestionarioForm() {
		const formQuestionarioCliente = this.form.get(
			"questionarioClienteGroup"
		) as FormGroup;
		this.questionarioCliente.questionarioPerguntaCliente.forEach(
			(questionarioPerguntaCliente, indexPergunta) => {
				if (questionarioPerguntaCliente) {
					if (questionarioPerguntaCliente.perguntaCliente) {
						if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "NS"
						) {
							const groupPerguntaName = `${this.groupNsPrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlNsPrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName)
										.valueChanges.pipe(takeUntil(this._destroy))
										.subscribe((v) => {
											for (const controlsKey in (
												formQuestionarioCliente.get(
													groupPerguntaName
												) as FormGroup
											).controls) {
												if (controlsKey !== controlRespostaName) {
													formQuestionarioCliente
														.get(groupPerguntaName)
														.get(controlsKey)
														.setValue(false, { emitEvent: false });
												}
											}
										});
								}
							);
						} else if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "ME"
						) {
							const groupPerguntaName = `${this.groupMePrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlMePrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
								}
							);
						} else if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SE"
						) {
							const groupPerguntaName = `${this.groupSePrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlSePrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName)
										.valueChanges.pipe(takeUntil(this._destroy))
										.subscribe((v) => {
											for (const controlsKey in (
												formQuestionarioCliente.get(
													groupPerguntaName
												) as FormGroup
											).controls) {
												if (controlsKey !== controlRespostaName) {
													formQuestionarioCliente
														.get(groupPerguntaName)
														.get(controlsKey)
														.setValue(false, { emitEvent: false });
												}
											}
										});
								}
							);
						} else if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SN"
						) {
							const groupPerguntaName = `${this.groupSnPrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlSnPrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName)
										.valueChanges.pipe(takeUntil(this._destroy))
										.subscribe((v) => {
											for (const controlsKey in (
												formQuestionarioCliente.get(
													groupPerguntaName
												) as FormGroup
											).controls) {
												if (controlsKey !== controlRespostaName) {
													formQuestionarioCliente
														.get(groupPerguntaName)
														.get(controlsKey)
														.setValue(false, { emitEvent: false });
												}
											}
										});
								}
							);
						} else if (
							questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "TE"
						) {
							const groupPerguntaName = `${this.groupTePrefix}${indexPergunta}`;
							formQuestionarioCliente.addControl(
								groupPerguntaName,
								new FormGroup({})
							);
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente.forEach(
								(_, indexResposta) => {
									const controlRespostaName = `${this.controlTePrefix}${indexResposta}`;
									(
										formQuestionarioCliente.get(groupPerguntaName) as FormGroup
									).addControl(controlRespostaName, new FormControl());
								}
							);
						}
					}
				}
			}
		);
	}

	private validaAndConvertQuestoesObrigatorias(): boolean {
		const formQuestionarioCliente = this.form.get(
			"questionarioClienteGroup"
		) as FormGroup;
		for (
			let indexPergunta = 0;
			indexPergunta <
			this.questionarioCliente.questionarioPerguntaCliente.length;
			indexPergunta++
		) {
			const questionarioPerguntaCliente =
				this.questionarioCliente.questionarioPerguntaCliente[indexPergunta];
			if (questionarioPerguntaCliente) {
				if (questionarioPerguntaCliente.perguntaCliente) {
					if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "NS"
					) {
						const groupPerguntaName = `${this.groupNsPrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlNsPrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaOpcao =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (
							!hasAswer &&
							questionarioPerguntaCliente.perguntaCliente.obrigatoria
						) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					} else if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "ME"
					) {
						const groupPerguntaName = `${this.groupMePrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlMePrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaOpcao =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (
							!hasAswer &&
							questionarioPerguntaCliente.perguntaCliente.obrigatoria
						) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					} else if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SE"
					) {
						const groupPerguntaName = `${this.groupSePrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlSePrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaOpcao =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (
							!hasAswer &&
							questionarioPerguntaCliente.perguntaCliente.obrigatoria
						) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					} else if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SN"
					) {
						const groupPerguntaName = `${this.groupSnPrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlSnPrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaOpcao =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (
							!hasAswer &&
							questionarioPerguntaCliente.perguntaCliente.obrigatoria
						) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					} else if (
						questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "TE"
					) {
						const groupPerguntaName = `${this.groupTePrefix}${indexPergunta}`;
						let hasAswer = false;
						for (
							let indexResposta = 0;
							indexResposta <
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
								.length;
							indexResposta++
						) {
							const questionarioClienteResposta =
								questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
									indexResposta
								];
							const controlRespostaName = `${this.controlTePrefix}${indexResposta}`;
							if (
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value
							) {
								hasAswer = true;
								questionarioClienteResposta.respostaTextual =
									formQuestionarioCliente
										.get(groupPerguntaName)
										.get(controlRespostaName).value;
							}
						}
						if (
							!hasAswer &&
							questionarioPerguntaCliente.perguntaCliente.obrigatoria
						) {
							this.snotifyService.error(
								`A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`
							);
							return false;
						}
					}
				}
			}
		}
		return true;
	}

	private validaRespondeuTodasQuestoes(): { valid: boolean; message?: string } {
		const formQuestionarioCliente = this.form.get(
			"questionarioClienteGroup"
		) as FormGroup;
		for (
			let indexPergunta = 0;
			indexPergunta <
			this.questionarioCliente.questionarioPerguntaCliente.length;
			indexPergunta++
		) {
			const questionarioPerguntaCliente =
				this.questionarioCliente.questionarioPerguntaCliente[indexPergunta];
			if (questionarioPerguntaCliente) {
				if (questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "NS") {
					const groupPerguntaName = `${this.groupNsPrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlNsPrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaOpcao =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				} else if (
					questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "ME"
				) {
					const groupPerguntaName = `${this.groupMePrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlMePrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaOpcao =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				} else if (
					questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SE"
				) {
					const groupPerguntaName = `${this.groupSePrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlSePrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaOpcao =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				} else if (
					questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "SN"
				) {
					const groupPerguntaName = `${this.groupSnPrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlSnPrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaOpcao =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				} else if (
					questionarioPerguntaCliente.perguntaCliente.tipoPergunta === "TE"
				) {
					const groupPerguntaName = `${this.groupTePrefix}${indexPergunta}`;
					let hasAswer = false;
					for (
						let indexResposta = 0;
						indexResposta <
						questionarioPerguntaCliente.perguntaCliente.respostaPergCliente
							.length;
						indexResposta++
					) {
						const questionarioClienteResposta =
							questionarioPerguntaCliente.perguntaCliente.respostaPergCliente[
								indexResposta
							];
						const controlRespostaName = `${this.controlTePrefix}${indexResposta}`;
						if (
							formQuestionarioCliente
								.get(groupPerguntaName)
								.get(controlRespostaName).value
						) {
							hasAswer = true;
							questionarioClienteResposta.respostaTextual =
								formQuestionarioCliente
									.get(groupPerguntaName)
									.get(controlRespostaName).value;
						}
					}
					if (!hasAswer) {
						return {
							valid: false,
							message: `A pergunta '${questionarioPerguntaCliente.perguntaCliente.descricao}' deve ser respondida!`,
						};
					}
				}
			}
		}
		return { valid: true };
	}

	get urlFoto() {
		if (
			this.adicionarClienteService.clienteSelecionado &&
			this.adicionarClienteService.clienteSelecionado.urlFoto
		) {
			return this.adicionarClienteService.clienteSelecionado.urlFoto;
		} else {
			return "";
		}
	}

	get pageTitle() {
		if (
			this.adicionarClienteService.clienteSelecionado &&
			this.adicionarClienteService.clienteSelecionado.nome
		) {
			return `cadastro cliente: ${this.adicionarClienteService.clienteSelecionado.nome}`;
		} else {
			return "Adicionar cliente";
		}
	}

	tipoVinculoApresentar(tipo) {
		if (!tipo) {
			return "";
		}
		const tipoVinculo = this.tipoVinculoList.find((x) => x.codigo === tipo);
		return tipoVinculo.descricao;
	}

	adicionarVinculo() {
		if (!this.permiteAdicionarVinculo()) {
			this.snotifyService.error(
				'Você não possui a permissão "2.29 - Vínculos de cliente e de colaborador"'
			);
			return;
		}

		const tipoVinculo = this.form.get("tipoVinculo").value;
		if (!tipoVinculo) {
			this.snotifyService.error("Selecione o tipo de vinculo");
			return;
		}
		const codigoColaborador = this.form.get("colaborador").value;
		if (!codigoColaborador || codigoColaborador === 0) {
			this.snotifyService.error("Selecione o colaborador");
			return;
		}

		const existe = this.vinculosList.find(
			(x) =>
				x.codigoColaborador === codigoColaborador &&
				x.tipoVinculo === tipoVinculo
		);
		if (existe) {
			this.snotifyService.info("Esse vínculo já foi adicionado.");
			return;
		}

		const colaborador = this.colaboradorList.find(
			(x) => x.codigo === codigoColaborador
		);

		const vinculo = {
			tipoVinculo,
			codigoColaborador,
			nomeColaborador: colaborador.descricao,
		};
		this.vinculosList.push(vinculo);
		this.form.get("colaborador").setValue(null);
	}

	removerVinculo(index) {
		this.vinculosList.splice(index, 1);
	}

	permiteAdicionarVinculo(): any {
		const permition = this.permissao2_29_vinculo;
		const isPermited =
			permition &&
			permition.tipoPermissoes.find(
				(tp) =>
					tp === PerfilRecursoPermissoTipo.INCLUIR ||
					tp === PerfilRecursoPermissoTipo.EDITAR ||
					tp === PerfilRecursoPermissoTipo.TOTAL_EXCETO_EXCLUIR ||
					tp === PerfilRecursoPermissoTipo.TOTAL
			);
		const retor = isPermited !== undefined && isPermited;
		return retor;
	}

	private notificar(string: string) {
		try {
			this.sessionService.notificarRecursoEmpresa(string);
		} catch (e) {
			console.error(e);
		}
	}
}
