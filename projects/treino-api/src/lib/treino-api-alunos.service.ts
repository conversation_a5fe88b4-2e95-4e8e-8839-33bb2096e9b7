import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { CryptoService } from "sdk";
import { v4 as uuidv4 } from "uuid";
import {
	Aluno,
	AlunoAnexoAvaliacaoZW,
	AlunoAppInfo,
	AlunoAtestadoZW,
	AlunoBase,
	AlunoBaseNumero,
	AlunoNotificacaoZW,
	AlunoObservacaoZW,
	AlunoOlympia,
	AlunoPesquisa,
	AlunoTreinoDetalhes,
	LinhaTempoAlunoEvento,
} from "./aluno.model";
import { AvaliacaoBioimpedancia } from "./avaliacao-bioimpedancia.model";
import { ApiResponseList, ApiResponseSingle } from "./base.model";
import { TreinoApiBaseService } from "./treino-api-base.service";
import { TreinoApiModule } from "./treino-api.module";

@Injectable({
	providedIn: TreinoApiModule,
})
export class TreinoApiAlunosService {
	constructor(
		private restService: TreinoApiBaseService,
		private cryptoService: CryptoService
	) {}

	obterClienteMensagem(
		alunoId: number,
		tipoMensagem: string
	): Observable<Array<string>> {
		return this.restService
			.get(`alunos/${alunoId}/${tipoMensagem}/mensagem`)
			.pipe(
				map((result: ApiResponseList<string>) => {
					return result.content;
				})
			);
	}

	listaRapidaAcessos(tipo: number, limite: number): Observable<any> {
		const params: any = { tipo, limite };
		return this.restService.get(`alunos/lista-rapida-acessos`, { params }).pipe(
			map((result: ApiResponseList<string>) => {
				return result.content;
			})
		);
	}

	configRapidaAcessosNotificacao(tipo, valor): Observable<any> {
		return this.restService.post(
			`alunos/lista-rapida-acessos-notf/${tipo}/${valor}`,
			{}
		);
	}

	obterAtestadosDoAluno(alunoId: number): Observable<any> {
		return this.restService.get(`alunos/${alunoId}/atestados`).pipe(
			map((result: ApiResponseList<AlunoAtestadoZW>) => {
				return result.content;
			}),
			catchError((error) => {
				console.error(
					"Não foi possível consultar os atestados do aluno. " +
						error.error.meta.message
				);
				return new Observable((observer) => {
					observer.next([]);
					observer.complete();
				});
			})
		);
	}

	obterObservacoesDoAluno(alunoId: number): Observable<AlunoObservacaoZW[]> {
		return this.restService.get(`alunos/${alunoId}/observacoes`).pipe(
			map((result: ApiResponseList<AlunoObservacaoZW>) => {
				return result.content;
			})
		);
	}

	obterObservacoesDoAlunoPorMatricula(
		matricula: string
	): Observable<AlunoObservacaoZW[]> {
		return this.restService
			.get(`alunos/${matricula}/observacoes?matricula=true`)
			.pipe(
				map((result: ApiResponseList<AlunoObservacaoZW>) => {
					return result.content;
				})
			);
	}

	obterNotificacoesDoAluno(alunoId: number): Observable<any> {
		return this.restService.get(`alunos/${alunoId}/notificacoesAluno`).pipe(
			map((result: ApiResponseList<AlunoNotificacaoZW>) => {
				return result.content;
			}),
			catchError((error) => {
				console.error(
					"Não foi possível consultar as notificações do aluno: " +
						error.error.meta.message
				);
				return new Observable((observer) => {
					observer.next([]);
					observer.complete();
				});
			})
		);
	}

	cadastrarObservacaoDoAluno(
		alunoId: number,
		dto
	): Observable<AlunoObservacaoZW> {
		return this.restService.post(`alunos/${alunoId}/observacoes`, dto).pipe(
			map((result: ApiResponseSingle<AlunoObservacaoZW>) => {
				return result.content;
			})
		);
	}

	obterAnexosDeAvaliacao(
		alunoId: number,
		buscarAnexoZw: boolean
	): Observable<AlunoAnexoAvaliacaoZW[]> {
		const params: any = {};
		params.buscarAnexoZw = buscarAnexoZw;
		return this.restService
			.get(`alunos/${alunoId}/anexosAvaliacao`, { params })
			.pipe(
				map((result: ApiResponseList<AlunoAnexoAvaliacaoZW>) => {
					return result.content;
				})
			);
	}

	detalharAnexo(
		alunoId: number,
		anexoId: number
	): Observable<AlunoAnexoAvaliacaoZW[]> {
		return this.restService
			.get(`alunos/${alunoId}/anexosAvaliacao/${anexoId}`)
			.pipe(
				map((result: ApiResponseList<AlunoAnexoAvaliacaoZW>) => {
					return result.content;
				})
			);
	}

	cadastrarAnexoDeAvaliacao(
		alunoId: number,
		dto
	): Observable<AlunoAnexoAvaliacaoZW> {
		return this.restService.post(`alunos/${alunoId}/anexosAvaliacao`, dto).pipe(
			map((result: ApiResponseSingle<AlunoAnexoAvaliacaoZW>) => {
				return result.content;
			})
		);
	}

	importarAvaliacaoBioimpedancia(dto): Observable<AvaliacaoBioimpedancia> {
		return this.restService
			.post(`avaliacoes-fisica/extrairAvaliacaoBioimpedancia`, dto)
			.pipe(
				map((result: ApiResponseSingle<AvaliacaoBioimpedancia>) => {
					return result.content;
				})
			);
	}

	removerAnexoDeAvaliacao(anexoId: number): Observable<boolean> {
		return this.restService.delete(`alunos/anexosAvaliacao/${anexoId}`).pipe(
			map((result: ApiResponseSingle<any>) => {
				return true;
			})
		);
	}

	removerObservacao(observacaoId: number): Observable<boolean> {
		return this.restService.delete(`alunos/observacoes/${observacaoId}`).pipe(
			map((result: ApiResponseSingle<any>) => {
				return true;
			})
		);
	}

	cadastrarAluno(aluno): Observable<Aluno> {
		return this.restService.post(`alunos`, aluno).pipe(
			map((result: ApiResponseSingle<Aluno>) => {
				return result.content;
			}),
			catchError(() => {
				return new Observable((observer) => {
					observer.next("usuario_duplicado");
					observer.complete();
				});
			})
		);
	}

	atualizarAluno(id, aluno): Observable<Aluno> {
		return this.restService.put(`alunos/${id}`, aluno).pipe(
			map((response: ApiResponseSingle<Aluno>) => {
				return response.content;
			}),
			catchError((error, caught) => {
				return new Observable((observer) => {
					if (error.error.meta.error === "erro_aluno_alterar") {
						observer.next(error.error.meta.message);
					} else {
						observer.next(error.error.meta.error);
					}
					observer.complete();
				});
			})
		);
	}

	criarUsuarioMovelAluno(usuario: {
		alunoId: number;
		username: string;
		password: string;
	}): Observable<any> {
		return this.restService.post(`alunos/usuario`, usuario).pipe(
			map((response: ApiResponseSingle<any>) => {
				return true;
			}),
			catchError((error, caught) => {
				return new Observable((observer) => {
					observer.next(error.error.meta.message);
					observer.complete();
				});
			})
		);
	}

	obterAluno(id): Observable<AlunoBase> {
		// cria payload com matricula, UUID nonce, e timestamp
		const payload = {
			id,
			nonce: uuidv4(),
			timestamp: Date.now(),
		};

		const encryptedPayload = this.cryptoService.encrypt(
			JSON.stringify(payload)
		);

		return this.restService
			.post(`alunos/p9Q2t9stT45sc9v3RT85s6dgFifbjuoHFUf0ykjGyio`, {
				data: encryptedPayload,
			})
			.pipe(
				map((result: any) => {
					// verifica se response esta criptografado
					if (result.content && typeof result.content === "string") {
						try {
							// tenta decriptografar o response
							const decryptedContent = JSON.parse(
								this.cryptoService.decrypt(result.content)
							);
							return decryptedContent;
						} catch (error) {
							// se falhar, retornar o valor original
							console.error("Falha ao decryptar response:", error);
							return result.content;
						}
					}
					return result.content;
				})
			);
	}

	reenviarEmailConfirmacaoApp(id) {
		return this.restService.get(`alunos/${id}/reenviar-confirmacao-app`).pipe(
			map((result: any) => {
				return result.content;
			}),
			catchError((error, caught) => {
				return new Observable((observer) => {
					observer.next(error.error.meta.message);
					observer.complete();
				});
			})
		);
	}

	reenviarEmailConfirmacaoAppV2(id): Observable<any> {
		return this.restService.get(`alunos/${id}/reenviar-confirmacao-app`);
	}

	obterDetalhesTreinoAluno(alunoId): Observable<any> {
		// cria payload com alunoId, UUID nonce, e timestamp
		const payload = {
			alunoId,
			nonce: uuidv4(),
			timestamp: Date.now(),
		};

		const encryptedPayload = this.cryptoService.encrypt(
			JSON.stringify(payload)
		);

		return this.restService
			.post(`alunos/rJ6urBkIMHhEfn0jpIfrUKtbWTbTN4MXAJxAsyJW9KB`, {
				data: encryptedPayload,
			})
			.pipe(
				map((result: ApiResponseSingle<AlunoTreinoDetalhes>) => {
					// verifica se response esta criptografado
					if (result.content && typeof result.content === "string") {
						try {
							// tenta decriptografar o response
							const decryptedContent = JSON.parse(
								this.cryptoService.decrypt(result.content)
							);
							return decryptedContent;
						} catch (error) {
							// se falhar, retornar o valor original
							console.error("Falha ao decryptar response:", error);
							return result.content;
						}
					}
					return result.content;
				}),
				catchError(() => {
					return new Observable((o) => {
						const out: AlunoTreinoDetalhes = null;
						o.next(out);
						o.complete();
					});
				})
			);
	}

	removerAluno(id): Observable<boolean> {
		return this.restService.delete(`alunos/${id}`).pipe(
			map((response: ApiResponseSingle<any>) => {
				return true;
			})
		);
	}

	editarSituacaoAluno(id, aluno): Observable<boolean> {
		return this.restService.put(`alunos/situacao/${id}`, aluno).pipe(
			map((response: ApiResponseSingle<any>) => {
				return true;
			})
		);
	}

	obterLinhaTempoAluno(
		matriculaAluno,
		dataInicio,
		dataFim,
		tiposEvento?
	): Observable<Array<LinhaTempoAlunoEvento>> {
		const params: any = {};
		params.dataInicio = dataInicio;
		params.dataFim = dataFim;
		if (tiposEvento) {
			params.tiposEvento = tiposEvento;
		}
		return this.restService
			.get(`alunos/linha-tempo/${matriculaAluno}`, { params })
			.pipe(
				map((response: ApiResponseList<LinhaTempoAlunoEvento>) => {
					return response.content;
				})
			);
	}

	importaAlunoOlympia(idOlympia): Observable<any> {
		return this.restService
			.get(`alunos/buscar-aluno-olympia/${idOlympia}`)
			.pipe(
				map((response: ApiResponseSingle<AlunoOlympia>) => {
					return response.content;
				}),
				catchError((erro, caught) => {
					return new Observable((o) => {
						o.next(erro.error);
						o.complete();
					});
				})
			);
	}

	validarCadastroOlympia(codigoExterno: string): Observable<any> {
		return this.restService
			.get(`alunos/validar-cadastro-olympia/${codigoExterno}`)
			.pipe(
				map((response: ApiResponseSingle<boolean>) => {
					return response.content;
				})
			);
	}

	obterAlunoMatricula(id): Observable<AlunoBase> {
		return this.restService.get(`alunos/matricula/${id}`).pipe(
			map((result: any) => {
				return result.content;
			})
		);
	}

	obterAlunoCompletoPorMatricula(matricula): Observable<AlunoBase> {
		// cria payload com matricula, UUID nonce, e timestamp
		const payload = {
			matricula,
			nonce: uuidv4(),
			timestamp: Date.now(),
		};

		const encryptedPayload = this.cryptoService.encrypt(
			JSON.stringify(payload)
		);

		return this.restService
			.post(`alunos/*******************************************`, {
				data: encryptedPayload,
			})
			.pipe(
				map((result: any) => {
					// verifica se response esta criptografado
					if (result.content && typeof result.content === "string") {
						try {
							// tenta decriptografar o response
							const decryptedContent = JSON.parse(
								this.cryptoService.decrypt(result.content)
							);
							return decryptedContent;
						} catch (error) {
							// se falhar, retornar o valor original
							console.error("Falha ao decryptar response:", error);
							return result.content;
						}
					}
					return result.content;
				})
			);
	}

	obterAlunosPesquisa(parametro: string): Observable<Array<AlunoPesquisa>> {
		const params: any = {};
		params.parametro = parametro;
		return this.restService.get("alunos/search", { params }).pipe(
			map((result: ApiResponseList<AlunoPesquisa>) => {
				return result.content;
			})
		);
	}

	atualizarUtilizacaoNonoDigitoWpp(
		id,
		utilizaNonoDigitoWpp
	): Observable<Array<AlunoBaseNumero>> {
		return this.restService
			.put(
				`alunos/atualizar-utilizacao-nono-digito-wpp/${id}`,
				utilizaNonoDigitoWpp
			)
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response.content;
				})
			);
	}

	obterClienteMensagemMatricula(
		matricula: string,
		tipoMensagem: string
	): Observable<Array<string>> {
		return this.restService
			.get(`alunos/${matricula}/${tipoMensagem}/mensagem-matricula`)
			.pipe(
				map((result: ApiResponseList<string>) => {
					return result.content;
				})
			);
	}

	deleteClienteMensagemMatricula(
		matricula: string,
		tipoMensagem: string
	): Observable<boolean> {
		return this.restService
			.delete(`alunos/${matricula}/${tipoMensagem}/mensagem`)
			.pipe(
				map((result: ApiResponseSingle<any>) => {
					return true;
				})
			);
	}

	cadastrarAvisoAluno(
		matricula: string,
		tipoMensagem: string,
		mensagem: string
	): Observable<AlunoObservacaoZW> {
		return this.restService
			.post(`alunos/${matricula}/${tipoMensagem}/mensagem`, mensagem)
			.pipe(
				map((result: ApiResponseSingle<any>) => {
					return result.content;
				})
			);
	}

	obterTodosAlunos(): Observable<ApiResponseList<AlunoBase>> {
		const params: any = {};
		return this.restService.get(`alunos`, { params }).pipe(
			map((response: ApiResponseList<AlunoBase>) => {
				return response;
			})
		);
	}

	obterInfoAlunoApp(
		alunoid,
		codigoPessoaZW,
		codigoCliente
	): Observable<AlunoAppInfo> {
		const params: any = {};
		if (alunoid) {
			params.alunoid = alunoid;
		}
		if (codigoPessoaZW) {
			params.pessoa = codigoPessoaZW;
		}
		if (codigoCliente) {
			params.cliente = codigoCliente;
		}
		return this.restService.get(`alunos/alunoApp`, { params }).pipe(
			map((response: ApiResponseSingle<AlunoAppInfo>) => {
				return response.content;
			})
		);
	}

	editarProfessor(matricula, idProfessor): Observable<any> {
		return this.restService
			.put(`alunos/professor/${matricula}`, {
				professorId: idProfessor && idProfessor > 0 ? idProfessor : null,
			})
			.pipe(
				map((responses: ApiResponseSingle<any>) => {
					return responses.content;
				})
			);
	}

	gerarPdfAvaliacaoProgresso(
		matricula: number,
		alunoId: number,
		fichaId: number,
		avaliacaoAlunoRespondidaId: number
	): Observable<any> {
		const params = {
			matricula: String(matricula),
			alunoId: String(alunoId),
			fichaId: String(fichaId),
			avaliacaoAlunoRespondidaId: String(avaliacaoAlunoRespondidaId),
		};

		return this.restService
			.get("alunos/gerar-pdf-avaliacao-progresso-aluno", { params })
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response;
				}),
				catchError((error) => {
					return new Observable((observer) => {
						observer.error(error);
						observer.complete();
					});
				})
			);
	}

	validarSincronizacaoAlunoZwTr(codClienteZw: number): Observable<any> {
		const body = {
			codigoClienteZw: codClienteZw,
		};

		return this.restService
			.post(`alunos/validar-sincronizacao-aluno-zw-tr`, body)
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return true;
				}),
				catchError((error, caught) => {
					return new Observable((observer) => {
						observer.next(error.error.meta.message);
						observer.complete();
					});
				})
			);
	}

	validarSincronizacaoTodosAlunosZwTr(considerarVisitante): Observable<any> {
		return this.restService
			.post(`manutencao/validar-sincronizacao-todos-alunos-zw-tr`, null, {
				params: { considerarVisitante },
			})
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return true;
				}),
				catchError((error, caught) => {
					return new Observable((observer) => {
						observer.next(error.error.meta.message);
						observer.complete();
					});
				})
			);
	}
}
