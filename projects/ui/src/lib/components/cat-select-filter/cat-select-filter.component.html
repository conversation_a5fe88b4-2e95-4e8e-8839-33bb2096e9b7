<input [formControl]="control" class="cat-select-filter" type="hidden" />

<div *ngIf="label" [ngClass]="{ small: small }" class="pacto-label">
	{{ label }}
</div>

<div
	#currentElRef
	(click)="currentClickHandler($event)"
	[ngClass]="{
		opened: optionsOpen,
		error: error,
		disabled: control.disabled,
		small: small
	}"
	class="current-value"
	id="{{ id }}">
	<!-- CURRENT SELECTION -->
	<div class="current-wrapper">
		<pacto-cat-person-avatar
			*ngIf="currentOption && imageKey"
			[diameter]="small ? 24 : 34"
			[uri]="currentOption[imageKey]"></pacto-cat-person-avatar>
		<span #optionLabel class="option-label">
			{{ currentOption ? getOptionLabel(currentOption) : placeholder }}
		</span>
		<img
			*ngIf="!currentOption || !hasClearAction"
			class="double-arrow"
			src="pacto-ui/images/double-arrow.svg" />
		<i
			(click)="clearHandler($event)"
			*ngIf="currentOption && hasClearAction"
			class="clear-icon pct pct-x"></i>
	</div>

	<!-- LIST OF OPTIONS -->
	<div
		#selectArea
		[hidden]="!optionsOpen"
		[ngClass]="{ small: small }"
		class="options">
		<div class="d-flex align-items-center">
			<input
				#filter
				[formControl]="filterFC"
				[placeholder]="'form.filter' | translate"
				class="filter-input"
				id="{{ this.id + '-filter' }}" />
		</div>

		<div
			class="scroll-container"
			pactoCatSmoothScroll
			[maxHeight]="'250px'"
			*ngIf="options && options.length">
			<div
				class="option"
				id="{{ this.id + '-' + index }}"
				*ngFor="let option of filteredOptions; let index = index"
				(click)="selectOptionHandler(option)">
				<div class="option-container" *ngIf="!selectItem?.templateRef">
					<pacto-cat-person-avatar
						*ngIf="imageKey"
						[uri]="option[imageKey]"
						[diameter]="small ? 24 : 34"></pacto-cat-person-avatar>
					<div class="option-label-container">
						<span
							*ngIf="option.tipo"
							class="option-label"
							style="line-height: normal">
							{{ getOptionLabel(option) }}
						</span>
						<span *ngIf="!option.tipo" class="option-label">
							{{ getOptionLabel(option) }}
						</span>
						<div
							*ngIf="option.tipo && subLabel"
							class="option-sub-label-container">
							<span class="option-sub-label-tipo">Tipo:</span>
							<span class="option-sub-label">
								{{ getOptionSubLabel(option) }}
							</span>
						</div>
					</div>
				</div>

				<div *ngIf="selectItem?.templateRef">
					<ng-template
						*ngTemplateOutlet="
							selectItem.templateRef;
							context: { item: option }
						"></ng-template>
				</div>
			</div>
		</div>

		<div *ngIf="!loading && !options.length" class="empty-state">
			Nenhum resultado.
		</div>
	</div>
</div>
