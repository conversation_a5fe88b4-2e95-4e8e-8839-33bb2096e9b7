import { HttpParams } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { catchError, map } from "rxjs/operators";
import { AdmLegadoApiModule } from "../adm-legado-api.module";
import { AdmLegadoApiBaseService } from "../base/adm-legado-api-base.service";
import { AdmZwBootApiBaseService } from "../base/adm-zw-boot-api-base.service";
import {
	ApiResponseList,
	ApiResponseSingle,
	DataFiltro,
} from "../base/base.model";
import { Cliente } from "../models/cliente.model";
import { ConfiguracaoSistema } from "../models/configuracao-sistema.model";
import { ContratoAssinaturaDigitalModel } from "../models/contrato-assinatura-digital.model";
import { OpcoesContrato } from "../models/opcoes-contrato.model";
import { Parcela } from "../models/parcela.model";
import { Empresa } from "../models/usuario.model";

@Injectable({
	providedIn: AdmLegadoApiModule,
})
export class AdmLegadoTelaClienteService {
	constructor(
		private admZwBootApiBaseService: AdmZwBootApiBaseService,
		private admLegadoApiBaseService: AdmLegadoApiBaseService
	) {}

	emitirNfse(
		chave: string,
		codigoRecibo: number,
		codigoUsuario: number
	): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				recibo: codigoRecibo,
				usuario: codigoUsuario,
			},
			{
				params: {
					op: "emitirNFSe",
					key: chave,
				},
			}
		);
	}

	emitirNfce(
		chave: string,
		codigoRecibo: number,
		codigoUsuario: number
	): Observable<ApiResponseSingle<any>> {
		return this.admLegadoApiBaseService.post<ApiResponseSingle<any>>(
			`prest/tela-cliente`,
			{
				recibo: codigoRecibo,
				usuario: codigoUsuario,
			},
			{
				params: {
					op: "emitirNFCe",
					key: chave,
				},
			}
		);
	}

	imprimirRecibo(
		chave: string,
		codigoRecibo: number,
		codigoUsuario: number
	): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				recibo: codigoRecibo,
				usuario: codigoUsuario,
			},
			{
				params: {
					op: "imprimirRecibo",
					key: chave,
				},
			}
		);
	}

	enviarReciboEmail(
		chave: string,
		codigoRecibo: number,
		codigoUsuario: number,
		emails: string[]
	): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				recibo: codigoRecibo,
				usuario: codigoUsuario,
				emails,
			},
			{
				params: {
					op: "enviarReciboEmail",
					key: chave,
				},
			}
		);
	}

	imprimirContrato(chave: string, codigoContrato): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				contrato: codigoContrato,
			},
			{
				params: {
					op: "imprimirContrato",
					key: chave,
				},
			}
		);
	}

	enviarContratoEmail(
		chave: string,
		codigoContrato: number,
		usuario: number,
		emails: Array<string>,
		aditivo?: number
	): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				contrato: codigoContrato,
				usuario,
				emails,
				aditivo,
			},
			{
				params: {
					op: "enviarContratoEmail",
					key: chave,
					telaNova: "true",
				},
			}
		);
	}

	enviarContratoProdutoEmail(
		chave: string,
		codigoVendaAvulsa: number,
		codigoAulaAvulsaDiaria: number,
		codigoProduto: number,
		usuario: number,
		emails: Array<string>
	): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				vendaAvulsa: codigoVendaAvulsa,
				aulaAvulsaDiaria: codigoAulaAvulsaDiaria,
				produto: codigoProduto,
				usuario,
				emails,
			},
			{
				params: {
					op: "enviarContratoProdutoEmail",
					key: chave,
					telaNova: "true",
				},
			}
		);
	}

	alterarVigenciaProduto(
		chave: string,
		codigoProduto: number,
		usuario: number,
		dataFinal: number
	): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				movproduto: codigoProduto,
				usuario,
				final: new Date(dataFinal),
			},
			{
				params: {
					op: "alterarVigenciaFinalMovProduto",
					key: chave,
				},
			}
		);
	}

	alterarVencimentoParcelasObter(
		filtros: DataFiltro,
		chave: string,
		codigoContrato: number,
		cliente: number,
		usuario: number
	): Observable<any> {
		const page = filtros.page ? filtros.page : 0;
		const size = filtros.size ? filtros.size : 10;
		delete filtros.page;
		delete filtros.size;
		const params: any = {
			// page,
			// size,
			filters: JSON.stringify(filtros),
			op: "alterarVencimentoParcelasObter",
			key: chave,
		};
		return this.admLegadoApiBaseService
			.post<any>(
				`prest/tela-cliente`,
				{
					contrato: codigoContrato,
					usuario,
					cliente,
				},
				{
					params,
				}
			)
			.pipe(
				map((response) => {
					if (response.content) {
						response.content.forEach(
							(r) => (r.vencimento = Date.parse(`${r.vencimento} 00:00:00`))
						);
					}
					return response;
				})
			);
	}

	alterarVencimentoParcelas(
		chave: string,
		codigoContrato: number,
		usuario: number,
		parcelas: Array<Parcela>,
		diaVencimento?
	): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				contrato: codigoContrato,
				usuario,
				diaVencimento,
				parcelas,
			},
			{
				params: {
					op: "alterarVencimentoParcelas",
					key: chave,
				},
			}
		);
	}

	renovarMovProdutoObter(chave, usuario, codigoMovProduto) {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				movproduto: codigoMovProduto,
				usuario,
			},
			{
				params: {
					op: "renovarMovProdutoObter",
					key: chave,
				},
			}
		);
	}

	renovarMovProduto(
		chave,
		usuario,
		codigoMovProduto,
		codProduto,
		empresaLogada
	) {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				movproduto: codigoMovProduto,
				usuario,
				produto: codProduto,
				empresa: empresaLogada,
			},
			{
				params: {
					op: "renovarMovProduto",
					key: chave,
				},
			}
		);
	}

	getListaContratoRematricularERenovar(chave, codCliente, codEmpresaLogada) {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				codCliente,
				codEmpresaLogada,
			},
			{
				params: {
					op: "getListaContratoRematricularERenovar",
					key: chave,
				},
			}
		);
	}

	obterUniqueGympass(chave, codPessoa) {
		return this.gympass(chave, codPessoa, 0, 0, "obter");
	}

	obterTokenGogood(chave, codPessoa) {
		return this.gogood(chave, codPessoa, 0, 0, "obter");
	}

	cadastrarGympass(
		chave,
		codPessoa,
		codEmpresaLogada,
		codUsuarioLogado,
		tipo,
		token
	) {
		return this.gympass(
			chave,
			codPessoa,
			codEmpresaLogada,
			codUsuarioLogado,
			"cadastro",
			{
				tipo,
				token,
			}
		);
	}

	cadastrarGogood(
		chave,
		codPessoa,
		codEmpresaLogada,
		codUsuarioLogado,
		tokenUser,
		tokenAcademy
	) {
		return this.gogood(
			chave,
			codPessoa,
			codEmpresaLogada,
			codUsuarioLogado,
			"cadastro",
			{
				tokenUser,
				tokenAcademy,
			}
		);
	}

	excluirGympass(chave, codPessoa, codEmpresaLogada, codUsuarioLogado) {
		return this.gympass(
			chave,
			codPessoa,
			codEmpresaLogada,
			codUsuarioLogado,
			"excluir"
		);
	}

	excluirGogood(chave, codPessoa, codEmpresaLogada, codUsuarioLogado) {
		return this.gogood(
			chave,
			codPessoa,
			codEmpresaLogada,
			codUsuarioLogado,
			"excluir"
		);
	}

	autorizarGympass(chave, codPessoa, codEmpresaLogada, codUsuarioLogado) {
		return this.gympass(
			chave,
			codPessoa,
			codEmpresaLogada,
			codUsuarioLogado,
			"autorizar"
		);
	}

	historicoGympass(chave, codPessoa, codEmpresaLogada, codUsuarioLogado) {
		return this.gympass(
			chave,
			codPessoa,
			codEmpresaLogada,
			codUsuarioLogado,
			"historico"
		);
	}

	historicoGoGood(chave, codPessoa, codEmpresaLogada, codUsuarioLogado) {
		return this.gogood(
			chave,
			codPessoa,
			codEmpresaLogada,
			codUsuarioLogado,
			"historico"
		);
	}

	private gympass(
		chave,
		codPessoa,
		codEmpresaLogada,
		codUsuarioLogado,
		operacao,
		otherParams: any = {}
	) {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				pessoa: codPessoa,
				usuario: codUsuarioLogado,
				empresa: codEmpresaLogada,
				operacao,
				...otherParams,
			},
			{
				params: {
					op: "gympass",
					key: chave,
				},
			}
		);
	}

	private gogood(
		chave,
		codPessoa,
		codEmpresaLogada,
		codUsuarioLogado,
		operacao,
		otherParams: any = {}
	) {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				pessoa: codPessoa,
				usuario: codUsuarioLogado,
				empresa: codEmpresaLogada,
				operacao,
				...otherParams,
			},
			{
				params: {
					op: "gogood",
					key: chave,
				},
			}
		);
	}

	linkPagamentoCartao(chave, codCliente, codEmpresaLogada, codUsuarioLogado) {
		return this.linkCartao(
			chave,
			codCliente,
			codEmpresaLogada,
			codUsuarioLogado,
			"pagamento",
			null,
			null,
			1
		);
	}

	linkCadastro(chave, codCliente, codEmpresaLogada, codUsuarioLogado) {
		return this.linkCartao(
			chave,
			codCliente,
			codEmpresaLogada,
			codUsuarioLogado,
			"cadastro",
			null,
			null,
			null
		);
	}

	linkCartao(
		chave,
		codCliente,
		codEmpresaLogada,
		codUsuarioLogado,
		operacao,
		todasEmAberto,
		parcelasSelecionadas,
		numeroVezesParcelamentoOperadora,
		tipoCompartilhamento?: "whatsapp" | "copiar",
		linkGerado?: boolean
	) {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				cliente: codCliente,
				usuario: codUsuarioLogado,
				empresa: codEmpresaLogada,
				operacao,
				todasEmAberto,
				parcelasSelecionadas,
				tipoCompartilhamento,
				linkGerado,
				numeroVezesParcelamentoOperadora,
			},
			{
				params: {
					op: "linkCartao",
					key: chave,
				},
			}
		);
	}

	cadastrarSenhaDeAcesso(
		chave,
		codPessoa,
		liberarSenhaAcesso,
		codCliente,
		codEmpresa,
		senha,
		confirmarSenha
	) {
		return this.definirSenhaDeAcesso(chave, codPessoa, "cadastrar", {
			liberarSenhaAcesso,
			codCliente,
			codEmpresa,
			senha,
			confirmarSenha,
		});
	}

	obterLiberarSenhaAcesso(chave, codPessoa) {
		return this.definirSenhaDeAcesso(
			chave,
			codPessoa,
			"obterLiberarSenhaAcesso"
		);
	}

	private definirSenhaDeAcesso(chave, codPessoa, operacao, params: any = {}) {
		return this.admLegadoApiBaseService.post<any>(
			"prest/tela-cliente",
			{
				operacao,
				codPessoa,
				...params,
			},
			{
				params: {
					op: "definirSenhaDeAcesso",
					key: chave,
				},
			}
		);
	}

	buscarMensagemCatraca(chave, codCliente) {
		return this.bloqueioAcessoCatraca(chave, codCliente, "consultar");
	}

	gravarMensagemCatraca(
		chave,
		codCliente,
		clienteMensagem,
		codUsuarioResponsavel
	) {
		return this.bloqueioAcessoCatraca(
			chave,
			codCliente,
			"gravar",
			codUsuarioResponsavel,
			{
				clienteMensagem,
			}
		);
	}

	removerCatraca(chave, codCliente, codClienteMensagem, codUsuarioResponsavel) {
		return this.bloqueioAcessoCatraca(
			chave,
			codCliente,
			"excluir",
			codUsuarioResponsavel,
			{
				codigo: codClienteMensagem,
			}
		);
	}

	private bloqueioAcessoCatraca(
		chave,
		codCliente,
		operacao,
		codUsuarioResponsavel?,
		params: any = {}
	) {
		return this.admLegadoApiBaseService.post<any>(
			"prest/tela-cliente",
			{
				operacao,
				codCliente,
				codUsuarioResponsavel,
				...params,
			},
			{
				params: {
					op: "bloqueioAcessoCatraca",
					key: chave,
				},
			}
		);
	}

	listaLocalAcesso(chave, codEmpresa) {
		return this.registrarAcessoManual(
			chave,
			codEmpresa,
			undefined,
			undefined,
			"listaLocalAcesso"
		);
	}

	listaColetor(chave, codEmpresa, registrarAcesso) {
		return this.registrarAcessoManual(
			chave,
			codEmpresa,
			undefined,
			registrarAcesso,
			"listaColetor"
		);
	}

	consultarAcessosDia(chave, codEmpresa, registrarAcesso) {
		return this.registrarAcessoManual(
			chave,
			codEmpresa,
			undefined,
			registrarAcesso,
			"consultarAcessosDia"
		);
	}

	gravarAcessoManual(chave, codEmpresa, registrarAcesso) {
		return this.registrarAcessoManual(
			chave,
			codEmpresa,
			undefined,
			registrarAcesso,
			"gravar"
		);
	}

	private registrarAcessoManual(
		chave,
		codEmpresa,
		codLocalAcesso,
		registrarAcesso,
		operacao
	) {
		return this.admLegadoApiBaseService.post<any>(
			"prest/tela-cliente",
			{
				operacao,
				codEmpresa,
				codLocalAcesso,
				registrarAcesso,
			},
			{
				params: {
					op: "registrarAcessoManual",
					key: chave,
				},
			}
		);
	}

	obterDocumentosContrato(chave, codigoContrato): Observable<any> {
		return this.admLegadoApiBaseService.get(
			`/prest/tela-cliente?contrato=${codigoContrato}&op=obterDocumentosContrato&key=${chave}`
		);
	}

	upload(chave, data: ContratoAssinaturaDigitalModel) {
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente?key=${chave}&op=salvarImagens`,
			data
		);
	}

	notificarIntegracaoFoguete(
		chave: string,
		codCliente: number,
		codUsuario: number,
		sincronizacaoManual: boolean
	) {
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente?key=${chave}&op=notificarIntegracaoFoguete`,
			{
				tipo: "contratoAtivo",
				cliente: codCliente,
				usuario: codUsuario,
				sincronizacaoManual,
			}
		);
	}

	desmarcarHorario(
		chave,
		codigoHorarioTurma,
		codCliente,
		codContrato,
		codUsuario,
		dataDesmarcar,
		codTurmaDestino
	): Observable<any> {
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente?key=${chave}&op=desmarcarHorario`,
			{
				cliente: codCliente,
				horarioTurma: codigoHorarioTurma,
				contrato: codContrato,
				usuario: codUsuario,
				dataDesmarcar,
				codTurmaDestino,
			}
		);
	}

	private aulasContrato(
		filtros: DataFiltro,
		chave,
		codigoContrato,
		tipo,
		othersProps: any = {}
	): Observable<any> {
		const page = filtros.page ? filtros.page : 0;
		const size = filtros.size ? filtros.size : 10;
		delete filtros.page;
		delete filtros.size;
		const params: any = {
			page,
			size,
			filters: JSON.stringify(filtros),
			op: "aulasContrato",
			key: chave,
		};
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente`,
			{
				contrato: codigoContrato,
				tipo,
				...othersProps,
			},
			{
				params,
			}
		);
	}

	aulasContratoReposicoes(
		filtros: DataFiltro,
		chave,
		codigoContrato,
		codTurma
	): Observable<any> {
		return this.aulasContrato(filtros, chave, codigoContrato, "reposicoes", {
			turma: codTurma,
		});
	}

	aulasContratoFaltas(
		filtros: DataFiltro,
		chave,
		codigoContrato,
		codContratoModalidadeTurma
	): Observable<any> {
		return this.aulasContrato(filtros, chave, codigoContrato, "faltas", {
			contratoModalidadeTurma: codContratoModalidadeTurma,
		});
	}

	aulasContratoDesmarcadas(
		filtros: DataFiltro,
		chave,
		codigoContrato,
		codTurma
	): Observable<any> {
		return this.aulasContrato(filtros, chave, codigoContrato, "desmarcadas", {
			turma: codTurma,
		});
	}

	aulasContratoDesmarcadasContratoAnterior(
		filtros: DataFiltro,
		chave,
		codigoContrato,
		codContratoPassado,
		codEmpresaLogada
	): Observable<any> {
		return this.aulasContrato(
			filtros,
			chave,
			codigoContrato,
			"desmarcadasContratoPassado",
			{
				codEmpresaLogada,
				codContratoPassado,
			}
		);
	}

	alterarDadosContrato(
		chave,
		codigoContrato,
		codigoUsuario,
		codigoConsultor,
		tipoContrato,
		renovacaoAutomatica
	): Observable<any> {
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente?key=${chave}&op=alterarDadosContrato`,
			{
				contrato: codigoContrato,
				usuario: codigoUsuario,
				consultor: codigoConsultor,
				tipoContrato,
				renovacaoAutomatica,
			}
		);
	}

	imprimirContratoOperacao(
		chave,
		contratoOperacao,
		data
	): Observable<ApiResponseSingle<any>> {
		const queryParams = new HttpParams({
			fromObject: {
				key: chave,
				op: "imprimirContratoOperacao",
				contratoOperacao,
			},
		});

		return this.admLegadoApiBaseService.post<ApiResponseSingle<any>>(
			`prest/tela-cliente`,
			data,
			{ params: queryParams }
		);
	}

	estornarContratoOperacao(
		chave,
		codContratoOperacao,
		codUsuario
	): Observable<any> {
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente?key=${chave}&op=estornarContratoOperacao`,
			{
				contratoOperacao: codContratoOperacao,
				usuario: codUsuario,
			}
		);
	}

	enviarEmailCancelamentoContrato(
		chave,
		data
	): Observable<ApiResponseSingle<any>> {
		const queryParams = new HttpParams({
			fromObject: {
				key: chave,
				op: "enviarEmailCancelamentoContrato",
			},
		});

		return this.admLegadoApiBaseService.post<ApiResponseSingle<any>>(
			`prest/tela-cliente`,
			data,
			{ params: queryParams }
		);
	}

	desfazerCancelamento(
		chave,
		codContratoOperacao,
		codUsuario
	): Observable<any> {
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente?key=${chave}&op=desfazerCancelamento`,
			{
				contratoOperacao: codContratoOperacao,
				usuario: codUsuario,
			}
		);
	}

	opcoesContrato(
		chave,
		codContrato,
		codCliente,
		codEmpresaLogada,
		codUsuarioLogado,
		tipoConsulta = "mapaMostrar"
	): Observable<ApiResponseSingle<OpcoesContrato>> {
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente?key=${chave}&op=opcoesContrato`,
			{
				contrato: codContrato,
				cliente: codCliente,
				empresa: codEmpresaLogada,
				usuario: codUsuarioLogado,
				tipo: tipoConsulta,
			}
		);
	}

	configuracoesSistema(
		chave: string,
		codigo?: number
	): Observable<ApiResponseSingle<ConfiguracaoSistema>> {
		return this.admLegadoApiBaseService.get<
			ApiResponseSingle<ConfiguracaoSistema>
		>(
			`prest/tela-cliente?key=${chave}&op=configuracoesSistema&codigo=${codigo}`
		);
	}

	configuracoesEmpresa(
		chave: string,
		codEmpresa?: number
	): Observable<ApiResponseSingle<Empresa> | ApiResponseList<Empresa>> {
		return this.admLegadoApiBaseService.get<
			ApiResponseSingle<Empresa> | ApiResponseList<Empresa>
		>(
			`prest/tela-cliente?key=${chave}&op=configuracoesEmpresa&empresa=${codEmpresa}`
		);
	}

	consultarSaldoCredito(
		chave: string,
		codContrato: number,
		matricula: number
	): Observable<
		ApiResponseSingle<{
			saldoCreditoTreino: number;
			marcacoesFuturas: number;
		}>
	> {
		return this.admLegadoApiBaseService.get<
			ApiResponseSingle<{
				saldoCreditoTreino: number;
				marcacoesFuturas: number;
			}>
		>(
			`prest/tela-cliente?key=${chave}&op=consultarSaldoCredito&codContrato=${codContrato}&matricula=${matricula}`
		);
	}

	transferirDireitoContrato(
		chave,
		codUsuario,
		codCliente,
		codClienteDestino,
		codContrato
	): Observable<ApiResponseSingle<any>> {
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente?key=${chave}&op=transferirDireitoContrato`,
			{
				usuario: codUsuario,
				cliente: codCliente,
				clienteDestino: codClienteDestino,
				contrato: codContrato,
				tipo: "transferir",
			}
		);
	}

	transferirDireitoContratoConsultaClientes(
		chave,
		codUsuario,
		codEmpresa,
		term
	): Observable<ApiResponseList<Cliente>> {
		return this.admLegadoApiBaseService.post<ApiResponseList<Cliente>>(
			`prest/tela-cliente?key=${chave}&op=transferirDireitoContrato`,
			{
				usuario: codUsuario,
				empresa: codEmpresa,
				busca: term,
				tipo: "consulta",
			}
		);
	}

	consultarAlunosTransferenciaDeCredito(
		chave,
		codEmpresa,
		term
	): Observable<ApiResponseList<Cliente>> {
		return this.admZwBootApiBaseService.post<ApiResponseList<Cliente>>(
			`prest/tela-cliente?key=${chave}&op=consultarAlunosTransferenciaDeCredito`,
			{
				empresa: codEmpresa,
				busca: term,
			}
		);
	}

	transferirDireitoContratoRecuperar(
		chave,
		codUsuario,
		codEmpresa,
		codCliente,
		codContrato
	): Observable<ApiResponseSingle<any>> {
		return this.admLegadoApiBaseService.post(
			`prest/tela-cliente?key=${chave}&op=transferirDireitoContrato`,
			{
				usuario: codUsuario,
				empresa: codEmpresa,
				cliente: codCliente,
				contrato: codContrato,
				tipo: "recuperar",
			}
		);
	}

	consultarAvisos(
		chave: string,
		cliente: string,
		pagination: { size: string; page: string } = { size: "10", page: "0" }
	): Observable<ApiResponseList<any>> {
		const { size, page } = pagination;
		const queryParams = new HttpParams({
			fromObject: { key: chave, op: "consultarAvisos", cliente, size, page },
		});
		return this.admLegadoApiBaseService.post<ApiResponseList<any>>(
			`prest/tela-cliente`,
			null,
			{ params: queryParams }
		);
	}

	dadosPagamentoCliente(
		chave: string,
		cliente: string
	): Observable<ApiResponseSingle<any>> {
		const queryParams = new HttpParams({
			fromObject: { key: chave, op: "dadosPagamentoCliente", cliente },
		});
		return this.admLegadoApiBaseService.post<ApiResponseSingle<any>>(
			`prest/tela-cliente`,
			null,
			{ params: queryParams }
		);
	}

	liberarVagaTurma(
		chave,
		codContrato,
		codEmpresaContrato,
		codUsuarioLogado,
		vigenciaAteAjustada
	) {
		return this.admLegadoApiBaseService.get<ApiResponseSingle<any>>(
			`prest/tela-cliente`,
			{
				params: {
					op: "liberarVagaTurma",
					key: chave,
					codContrato,
					codEmpresaContrato,
					codUsuarioLogado,
					vigenciaAteAjustada: new Date(vigenciaAteAjustada).toISOString(),
				},
			}
		);
	}

	faltasTurmasRemovidas(
		chave,
		codContrato,
		vigenciaAteAjustada,
		codCliente,
		matricula
	) {
		return this.admLegadoApiBaseService.get<ApiResponseSingle<any>>(
			`prest/tela-cliente`,
			{
				params: {
					op: "faltasTurmasRemovidas",
					key: chave,
					codContrato,
					vigenciaAteAjustada: new Date(vigenciaAteAjustada).toISOString(),
					codCliente,
					matricula,
				},
			}
		);
	}

	buscarHorarioTurmaByCodigo(chave, codHorarioTurma) {
		return this.admLegadoApiBaseService.get<ApiResponseSingle<any>>(
			`prest/tela-cliente`,
			{
				params: {
					op: "buscarHorarioTurmaByCodigo",
					key: chave,
					codHorarioTurma,
				},
			}
		);
	}

	alterarMatricula(chave, data): Observable<ApiResponseSingle<any>> {
		const queryParams = new HttpParams({
			fromObject: { key: chave, op: "alterarMatricula" },
		});
		return this.admLegadoApiBaseService.post<ApiResponseSingle<any>>(
			`prest/tela-cliente`,
			data,
			{ params: queryParams }
		);
	}

	incluirControleCreditoTreino(
		chave,
		data
	): Observable<ApiResponseSingle<any>> {
		const queryParams = new HttpParams({
			fromObject: { key: chave, op: "incluirControleCreditoTreino" },
		});
		return this.admLegadoApiBaseService.post<ApiResponseSingle<any>>(
			`prest/tela-cliente`,
			data,
			{ params: queryParams }
		);
	}

	cancelarOperacao(
		chave,
		codContratoOperacao,
		codUsuarioLogado
	): Observable<any> {
		return this.admLegadoApiBaseService.get(`prest/tela-cliente`, {
			params: {
				codContratoOperacao,
				codUsuarioLogado,
				key: chave,
				op: "cancelarOperacao",
			},
		});
	}

	obterStatusConciliadora(chave, codMovPagamento: number): Observable<any> {
		return this.admLegadoApiBaseService.get(`prest/tela-cliente`, {
			params: {
				codMovPagamento: codMovPagamento.toString(),
				key: chave,
				op: "obterStatusConciliadora",
			},
		});
	}

	obterCheques(
		chave,
		codContrato: number,
		filtros: DataFiltro
	): Observable<any> {
		const page = filtros.page ? filtros.page : 0;
		const size = filtros.size ? filtros.size : 10;
		delete filtros.page;
		delete filtros.size;
		const params: any = {
			page,
			size,
			filters: JSON.stringify(filtros),
			codContrato: codContrato.toString(),
			op: "obterCheques",
			key: chave,
		};
		return this.admLegadoApiBaseService.get(`prest/tela-cliente`, { params });
	}

	imprimirContratoPrestacaoServico(
		chave,
		codMovProduto,
		codEmpresaLogada,
		codContratoPrestacaoServico,
		userOamd
	): Observable<any> {
		return this.admLegadoApiBaseService.get("prest/tela-cliente", {
			params: {
				op: "imprimirContratoPrestacaoServico",
				key: chave,
				codMovProduto,
				codEmpresaLogada,
				codContratoPrestacaoServico,
				userOamd,
			},
		});
	}

	imprimirReciboDevolucaoMovProduto(
		chave,
		codReciboDevolucao
	): Observable<any> {
		return this.admLegadoApiBaseService.get("prest/tela-cliente", {
			params: {
				op: "imprimirReciboDevolucaoMovProduto",
				key: chave,
				codReciboDevolucao,
			},
		});
	}

	consultarConvidados(chave: string, body: any): Observable<any> {
		body["tipo"] = "consultar";
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "convidado",
				key: chave,
				page: `0`,
				size: `10`,
			},
		});
	}

	consultarConvidadosCPF(chave: string, body: any): Observable<any> {
		body["tipo"] = "consultar_cpf";
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "convidado",
				key: chave,
				page: `0`,
				size: `10`,
			},
		});
	}

	salvarConvidado(chave: string, body: any): Observable<any> {
		body["tipo"] = "gravar";
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "convidado",
				key: chave,
			},
		});
	}

	convidadosInfo(chave: string, codigoCliente: number): Observable<any> {
		const body = {
			tipo: "informacoes",
			cliente: codigoCliente,
		};

		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "convidado",
				key: chave,
			},
		});
	}

	historicoConvidados(chave: string, codigoCliente: number): Observable<any> {
		const body = {
			tipo: "historico",
			cliente: codigoCliente,
		};

		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "convidado",
				key: chave,
			},
		});
	}

	public alterarFotoAluno(
		chave: string,
		pessoa: number,
		usuario: number,
		imagem: string
	): Observable<ApiResponseSingle<string>> {
		const body = { pessoa, usuario, imagem };
		const options = { params: { op: "alterarFotoAluno", key: chave } };
		return this.admLegadoApiBaseService.post<ApiResponseSingle<string>>(
			`prest/tela-cliente`,
			body,
			options
		);
	}

	public excluirFotoAluno(
		chave: string,
		pessoa: number,
		usuario: number
	): Observable<ApiResponseSingle<string>> {
		const body = { pessoa, usuario };
		const options = { params: { op: "excluirFotoAluno", key: chave } };
		return this.admLegadoApiBaseService.post<ApiResponseSingle<string>>(
			`prest/tela-cliente`,
			body,
			options
		);
	}

	public excluirAnexoZW(
		chave: string,
		pessoa: number,
		usuario: number
	): Observable<ApiResponseSingle<string>> {
		const body = { pessoa, usuario };
		const options = { params: { op: "excluirAnexoZW", key: chave } };
		return this.admLegadoApiBaseService.post<ApiResponseSingle<string>>(
			`prest/tela-cliente`,
			body,
			options
		);
	}

	public excluirClienteMensagemProdutoVencido(
		chave: string,
		clienteMensagem: number,
		pessoa: number,
		usuario: number
	): Observable<ApiResponseSingle<string>> {
		const body = { pessoa, usuario, clienteMensagem };
		const options = {
			params: { op: "excluirClienteMensagemProdutoVencido", key: chave },
		};
		return this.admLegadoApiBaseService.post<ApiResponseSingle<string>>(
			`prest/tela-cliente`,
			body,
			options
		);
	}

	public desbloquearMsgProdutoVencido(
		chave: string,
		clienteMensagem: number,
		pessoa: number,
		usuario: number
	): Observable<ApiResponseSingle<string>> {
		const body = { pessoa, usuario, clienteMensagem };
		const options = {
			params: { op: "desbloquearMsgProdutoVencido", key: chave },
		};
		return this.admLegadoApiBaseService.post<ApiResponseSingle<string>>(
			`prest/tela-cliente`,
			body,
			options
		);
	}

	public enviarReposicaoEmailSMS(
		chave: string,
		reposicao: number,
		usuario: number
	): Observable<ApiResponseSingle<string>> {
		const body = { tipo: "enviarReposicaoEmailSMS", usuario, reposicao };
		const options = { params: { op: "operacaoAula", key: chave } };
		return this.admLegadoApiBaseService.post<ApiResponseSingle<string>>(
			`prest/tela-cliente`,
			body,
			options
		);
	}

	public excluirReposicao(
		chave: string,
		reposicao: number,
		usuario: number
	): Observable<ApiResponseSingle<string>> {
		const body = { tipo: "excluirReposicao", usuario, reposicao };
		const options = { params: { op: "operacaoAula", key: chave } };
		return this.admLegadoApiBaseService.post<ApiResponseSingle<string>>(
			`prest/tela-cliente`,
			body,
			options
		);
	}

	public excluirAulaDesmarcada(
		chave: string,
		aulaDesmarcada: number,
		usuario: number
	): Observable<ApiResponseSingle<string>> {
		const body = { tipo: "excluirAulaDesmarcada", usuario, aulaDesmarcada };
		const options = { params: { op: "operacaoAula", key: chave } };
		return this.admLegadoApiBaseService.post<ApiResponseSingle<string>>(
			`prest/tela-cliente`,
			body,
			options
		);
	}

	alterarObjecaoCliente(
		chave,
		cliente,
		usuario,
		objecao
	): Observable<ApiResponseSingle<any>> {
		const body = { usuario, cliente, objecao };
		const options = { params: { op: "alterarObjecaoCliente", key: chave } };
		return this.admLegadoApiBaseService.post<ApiResponseSingle<string>>(
			`prest/tela-cliente`,
			body,
			options
		);
	}

	imprimirBoleto(
		chave: string,
		remessa_item: number,
		boleto: number
	): Observable<any> {
		const body = {
			tipo: "imprimir",
			remessa_item,
			boleto,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	imprimirBoletos(chave: string, boletos: any[]): Observable<any> {
		const body = {
			tipo: "imprimirBoletos",
			boletos,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	cancelarBoletos(
		chave: string,
		usuario: number,
		boletos: any[]
	): Observable<any> {
		const body = {
			tipo: "cancelarBoletos",
			usuario,
			boletos,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	enviarEmailBoletos(
		chave: string,
		usuario: number,
		boletos: any[]
	): Observable<any> {
		const body = {
			tipo: "emailBoletos",
			usuario,
			boletos,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	sincronizarBoletos(
		chave: string,
		usuario: number,
		boletos: any[]
	): Observable<any> {
		const body = {
			tipo: "sincronizarBoletos",
			usuario,
			boletos,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	imprimirBoletoBBOnline(chave: string, boleto: number): Observable<any> {
		const body = {
			tipo: "imprimirBBOnline",
			boleto,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	imprimirBoletoOnlineNaoRegistrado(
		chave: string,
		boleto: number,
		codUsuarioZW: number
	): Observable<any> {
		const body = {
			tipo: "imprimirBoletoOnlineNaoRegistrado",
			boleto,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	enviarEmailBoleto(
		chave: string,
		codUsuarioZW: number,
		remessa_item: number,
		boleto: number
	): Observable<any> {
		const body = {
			tipo: "email",
			remessa_item,
			boleto,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	enviarEmailBoletoBB(
		chave: string,
		empresa: string,
		codUsuarioZW: number,
		boleto: number
	): Observable<any> {
		const body = {
			tipo: "emailBBOnline",
			boleto,
			empresa,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	detalhesBoleto(
		chave: string,
		codUsuarioZW: number,
		boleto: number
	): Observable<any> {
		const body = {
			tipo: "detalhe",
			boleto,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	sincronizarBoleto(
		chave: string,
		codUsuarioZW: number,
		boleto: number
	): Observable<any> {
		const body = {
			tipo: "sincronizar",
			boleto,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	cancelarBoleto(
		chave: string,
		codUsuarioZW: number,
		boleto: number
	): Observable<any> {
		const body = {
			tipo: "cancelar",
			boleto,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	removerParcelaRemessaItem(
		chave: string,
		codUsuarioZW: number,
		codigo: number
	): Observable<any> {
		const body = {
			tipo: "remover_parcela",
			remessa_item: codigo,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "boleto",
				key: chave,
			},
		});
	}

	enviarEmailNotaFiscal(
		chave: string,
		codUsuarioZW: number,
		codNotaFiscal: number
	): Observable<any> {
		const body = {
			tipo: "email",
			notafiscal: codNotaFiscal,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "notafiscal",
				key: chave,
			},
		});
	}

	retentarTransacao(
		chave: string,
		codUsuarioZW: number,
		codTransacao: number
	): Observable<any> {
		const body = {
			tipo: "retentar",
			transacao: codTransacao,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "pactopay",
				key: chave,
			},
		});
	}

	sincronizarTransacao(
		chave: string,
		codUsuarioZW: number,
		codTransacao: number
	): Observable<any> {
		const body = {
			tipo: "sincronizar",
			transacao: codTransacao,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "pactopay",
				key: chave,
			},
		});
	}

	linkComprovanteCancelamentoTransacao(
		chave: string,
		codUsuarioZW: number,
		codTransacao: number
	): Observable<any> {
		const body = {
			tipo: "linkComprovanteCancelamento",
			transacao: codTransacao,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "pactopay",
				key: chave,
			},
		});
	}

	emailComprovanteCancelamentoTransacao(
		chave: string,
		codUsuarioZW: number,
		codTransacao: number
	): Observable<any> {
		const body = {
			tipo: "emailComprovanteCancelamento",
			transacao: codTransacao,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "pactopay",
				key: chave,
			},
		});
	}

	incluirConvidadoLancarConvite(
		chave: string,
		codEmpresa: number,
		codUsuarioZW: number,
		codCliente: number,
		dados: any
	): Observable<any> {
		const body = {
			tipo: "incluir_convidado",
			empresa: codEmpresa,
			usuario: codUsuarioZW,
			cliente: codCliente,
			dados,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "convidado",
				key: chave,
			},
		});
	}

	verificarClienteImportacao(
		chave: string,
		codUsuarioZW: number,
		codCliente: number
	): Observable<any> {
		const body = {
			tipo: "verificar",
			cliente: codCliente,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "importacao",
				key: chave,
			},
		});
	}

	desverificarClienteImportacao(
		chave: string,
		codUsuarioZW: number,
		codCliente: number
	): Observable<any> {
		const body = {
			tipo: "desverificar",
			cliente: codCliente,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "importacao",
				key: chave,
			},
		});
	}

	cacCliente(chave: string, codEmpresa: number): Observable<any> {
		const body = {
			tipo: "cac",
			empresa: codEmpresa,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "ltv",
				key: chave,
			},
		});
	}

	excluirAfastamentoContratoDependente(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		codAfastamentoContratoDependente: number
	): Observable<any> {
		const body = {
			tipo: "excluir",
			empresa: codEmpresa,
			usuario: codUsuario,
			afastamentoContratoDependente: codAfastamentoContratoDependente,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "contratoDependente",
				key: chave,
			},
		});
	}

	obterInfoAfastamentoContratoDependente(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		codContratoDependente: number
	): Observable<any> {
		const body = {
			tipo: "dados_ferias",
			empresa: codEmpresa,
			usuario: codUsuario,
			contratoDependente: codContratoDependente,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "contratoDependente",
				key: chave,
			},
		});
	}

	gravarAfastamentoContratoDependente(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		body: any
	): Observable<any> {
		const bodyE = {
			tipo: "gravar_afastamento",
			empresa: codEmpresa,
			usuario: codUsuario,
			...body,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "contratoDependente",
				key: chave,
			},
		});
	}

	validarRegrasAfastamentoContratoDependente(
		codigoContratoDependente: number,
		codEmpresa: number,
		codUsuario: number,
		tipoAfastamento: string,
		chave: string
	): Observable<any> {
		const body = {
			tipo: "validar_regras_afastamento",
			codigoContratoDependente,
			tipoAfastamento,
			empresa: codEmpresa,
			usuario: codUsuario,
		};
		const params = {
			op: "contratoDependente",
			key: chave,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params,
		});
	}

	obterInfoContratoDependente(
		chave: string,
		codEmpresa: number,
		codPessoa: number,
		codContrato: number
	): Observable<any> {
		const bodyE = {
			tipo: "info",
			empresa: codEmpresa,
			pessoa: codPessoa,
			contrato: codContrato,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "familiares",
				key: chave,
			},
		});
	}

	adicionarDependenteContratoDependente(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		codContrato: number,
		codClienteContrato: number,
		codClienteAdicionar: number
	): Observable<any> {
		const bodyE = {
			tipo: "adicionar_contrato_dependente",
			empresa: codEmpresa,
			usuario: codUsuario,
			contrato: codContrato,
			clienteContrato: codClienteContrato,
			clienteAdicionar: codClienteAdicionar,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "familiares",
				key: chave,
			},
		});
	}

	excluirDependenteContratoDependente(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		codContratoDependente: number
	): Observable<any> {
		const bodyE = {
			tipo: "excluir_contrato_dependente",
			empresa: codEmpresa,
			usuario: codUsuario,
			contratoDependente: codContratoDependente,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "familiares",
				key: chave,
			},
		});
	}

	alterarChaveDevolvidaArmario(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		codAluguelArmario: number,
		devolvida: boolean
	): Observable<any> {
		const bodyE = {
			tipo: "alterar_chave_devolvida",
			empresa: codEmpresa,
			usuario: codUsuario,
			devolvida,
			aluguelArmario: codAluguelArmario,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "armario",
				key: chave,
			},
		});
	}

	alterarContratoAssinadoArmario(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		codAluguelArmario: number,
		assinado: boolean
	): Observable<any> {
		const bodyE = {
			tipo: "alterar_contrato_assinado",
			empresa: codEmpresa,
			usuario: codUsuario,
			assinado,
			aluguelArmario: codAluguelArmario,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "armario",
				key: chave,
			},
		});
	}

	alterarRenovacaoAutomaticaMovProduto(
		chave: string,
		codUsuario: number,
		codMovProduto: number
	): Observable<any> {
		const bodyE = {
			tipo: "alterar_renovacao_automatica",
			usuario: codUsuario,
			movProduto: codMovProduto,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "movproduto",
				key: chave,
			},
		});
	}

	enviarContratoEmailAssinar(
		chave: string,
		codUsuario: number,
		codContrato: number,
		emails: Array<string>
	): Observable<any> {
		const bodyE = {
			tipo: "enviar_assinar",
			usuario: codUsuario,
			contrato: codContrato,
			emails,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "contrato",
				key: chave,
			},
		});
	}

	enviarContratoWhatsappAssinar(
		chave: string,
		codUsuario: number,
		codContrato: number,
		msg: string,
		telefone: string,
		linkVisualizarContrato: string
	): Observable<any> {
		const bodyRequisicao = {
			tipo: "enviar_assinar_whats",
			usuario: codUsuario,
			contrato: codContrato,
			msg: msg,
			telefone: telefone,
			linkVisualizarContrato: linkVisualizarContrato,
		};
		return this.admLegadoApiBaseService.post(
			"prest/tela-cliente",
			bodyRequisicao,
			{
				params: {
					op: "contrato",
					key: chave,
				},
			}
		);
	}

	obterPlanosAlterarPlano(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		dados: any
	): Observable<any> {
		const bodyE = {
			tipo: "obter_planos_alterar_plano",
			empresa: codEmpresa,
			usuario: codUsuario,
			dados,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "contrato",
				key: chave,
			},
		});
	}

	apresentarMenuContrato(
		chave: string,
		codEmpresa: number,
		codCliente: number,
		dados: any
	): Observable<any> {
		const bodyE = {
			tipo: "apresentar_menu_contrato",
			empresa: codEmpresa,
			cliente: codCliente,
			dados,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "contrato",
				key: chave,
			},
		});
	}

	obterInformacoesAlterarPlano(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		dados: any
	): Observable<any> {
		const bodyE = {
			tipo: "obter_info_alterar_plano",
			empresa: codEmpresa,
			usuario: codUsuario,
			dados,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "contrato",
				key: chave,
			},
		});
	}

	alterarPlano(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		dados: any
	): Observable<any> {
		const bodyE = {
			tipo: "alterar_plano",
			empresa: codEmpresa,
			usuario: codUsuario,
			dados,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "contrato",
				key: chave,
			},
		});
	}

	obterInformacoesAlterarVencimentoParcelasContrato(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		dados: any
	): Observable<any> {
		const bodyE = {
			tipo: "obter_info_alterar_vencimento_parcelas_contrato",
			empresa: codEmpresa,
			usuario: codUsuario,
			dados,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "contrato",
				key: chave,
			},
		});
	}

	calcularAlterarVencimentoParcelasContrato(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		dados: any
	): Observable<any> {
		const bodyE = {
			tipo: "calcular_alterar_vencimento_parcelas_contrato",
			empresa: codEmpresa,
			usuario: codUsuario,
			dados,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "contrato",
				key: chave,
			},
		});
	}

	alterarVencimentoParcelasContrato(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		dados: any
	): Observable<any> {
		const bodyE = {
			tipo: "alterar_vencimento_parcelas_contrato",
			empresa: codEmpresa,
			usuario: codUsuario,
			dados,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "contrato",
				key: chave,
			},
		});
	}

	sincronizarAlunoMgb(
		ctx: string,
		codCliente: string,
		empresaId: string
	): Observable<ApiResponseSingle<any>> {
		return this.admLegadoApiBaseService
			.post(`prest/pacto/mgb/sincronizar-aluno-mgb`, null, {
				params: {
					op: "sincronizar-aluno-mgb",
					chave: ctx,
					codigoCliente: codCliente,
					empresa: empresaId,
				},
			})
			.pipe(
				map((response: ApiResponseSingle<any>) => {
					return response;
				}),
				catchError((error) => {
					return new Observable((observer) => {
						observer.error(error);
						observer.complete();
					});
				})
			);
	}

	consultarDadosMgbAluno(chave, empresaId, codigoCliente): Observable<any> {
		return this.admLegadoApiBaseService.get(
			`/prest/pacto/mgb/consultar-dados-aluno?chave=${chave}&empresa=${empresaId}&codigoCliente=${codigoCliente}&op=consultarDadosMgbAluno`
		);
	}

	salvarHistoricoContato(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		codCliente: number,
		contato: any
	): Observable<any> {
		const bodyE = {
			tipo: "salvar",
			usuario: codUsuario,
			empresa: codEmpresa,
			cliente: codCliente,
			contato,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "crm",
				key: chave,
			},
		});
	}

	linkCadastrarCartaoHistoricoContato(
		chave: string,
		codEmpresa: number,
		codUsuario: number,
		codCliente: number
	): Observable<any> {
		const bodyE = {
			tipo: "link_cadastrar_cartao",
			usuario: codUsuario,
			empresa: codEmpresa,
			cliente: codCliente,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", bodyE, {
			params: {
				op: "crm",
				key: chave,
			},
		});
	}

	enviarEmailAplicativo(
		chave: string,
		codEmpresa: number,
		codUsuarioZW: number,
		codCliente: number,
		otherBody: any
	): Observable<any> {
		const body = {
			tipo: "email",
			empresa: codEmpresa,
			usuario: codUsuarioZW,
			cliente: codCliente,
			...otherBody,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "aplicativo",
				key: chave,
			},
		});
	}

	imprimirNotaFiscalSesi(
		chave: string,
		codUsuarioZW: number,
		codNotaFiscal: number
	): Observable<any> {
		const body = {
			tipo: "imprimir_nota_fiscal",
			notafiscal: codNotaFiscal,
			usuario: codUsuarioZW,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "sesi",
				key: chave,
			},
		});
	}

	validarTotalPass(chave, codPessoa, codEmpresaLogada, codUsuarioLogado) {
		return this.totalPass(
			chave,
			codPessoa,
			codEmpresaLogada,
			codUsuarioLogado,
			"validar"
		);
	}

	desvincularAlunoTotalpass(
		chave,
		codPessoa,
		codEmpresaLogada,
		codUsuarioLogado
	) {
		return this.totalPass(
			chave,
			codPessoa,
			codEmpresaLogada,
			codUsuarioLogado,
			"desvincular"
		);
	}

	validarParcelaAbertaComBoletoPendente(
		chave: string,
		codContrato: number
	): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{},
			{
				params: {
					op: "validarParcelaAbertaComBoletoPendente",
					key: chave,
					codContrato: `${codContrato}`,
				},
			}
		);
	}

	private totalPass(
		chave,
		codPessoa,
		codEmpresaLogada,
		codUsuarioLogado,
		operacao,
		otherParams: any = {}
	) {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				pessoa: codPessoa,
				usuario: codUsuarioLogado,
				empresa: codEmpresaLogada,
				operacao,
				...otherParams,
			},
			{
				params: {
					op: "totalpass",
					key: chave,
				},
			}
		);
	}

	enviarEmailReciboCancelamentoCobrancaGetcard(
		chave: string,
		codigoRecibo: number,
		mensagem: string,
		email: string,
		empresa: string
	): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				recibo: codigoRecibo,
				mensagem,
				email,
				empresa,
			},
			{
				params: {
					op: "enviarEmailReciboCancelamentoCobrancaGetcard",
					key: chave,
				},
			}
		);
	}

	apresentarAtivarAcessoConvidado(
		chave: string,
		cliente: number,
		usuario: number
	): Observable<any> {
		const body = {
			operacao: "apresentar",
			usuario,
			cliente,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "acesso_convidado",
				key: chave,
			},
		});
	}

	ativarAcessoConvidado(
		chave: string,
		cliente: number,
		usuario: number
	): Observable<any> {
		const body = {
			operacao: "ativar",
			usuario,
			cliente,
		};
		return this.admLegadoApiBaseService.post("prest/tela-cliente", body, {
			params: {
				op: "acesso_convidado",
				key: chave,
			},
		});
	}

	validarLinkPagamento(chave: string, codCliente: number): Observable<any> {
		return this.admLegadoApiBaseService.post<any>(
			`prest/tela-cliente`,
			{
				cliente: codCliente,
			},
			{
				params: {
					op: "validarLinkPagamento",
					key: chave,
				},
			}
		);
	}
}
