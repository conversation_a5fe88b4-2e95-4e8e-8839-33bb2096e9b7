import {
	ChangeDetectorRef,
	Component,
	Inject,
	OnInit,
	Optional,
} from "@angular/core";
import { Router } from "@angular/router";
import { Observable, Subscription } from "rxjs";
import {
	PactoLayoutSDKWrapper,
	PlataformaMenuV2Config,
} from "../../../sdk-wrapper/sdk-wrappers";
import { BUTTON_TYPE } from "ui-kit";
import { LayoutNavigationService } from "../../../navigation/layout-navigation.service";
import {
	PlataformModuleConfig,
	PlatformMenuItem,
} from "../../../navigation/models";

@Component({
	selector: "pacto-user-profile",
	templateUrl: "./user-profile.component.html",
	styleUrls: ["./user-profile.component.scss"],
})
export class UserProfileComponent implements OnInit {
	private subscription: Subscription;
	configuracao: PlataformaMenuV2Config;
	buttonType = BUTTON_TYPE;
	isUsuarioPactoSolucoes = false;

	constructor(
		@Optional() private pactoLayoutSDKWrapper: PactoLayoutSDKWrapper,
		private layoutNavigationService: LayoutNavigationService,
		private router: Router,
		private cd: ChangeDetectorRef
	) {}

	ngOnInit() {
		if (this.pactoLayoutSDKWrapper) {
			this.subscription = this.pactoLayoutSDKWrapper
				.getConfig()
				.subscribe((config) => {
					this.configuracao = config;
				});
			if (this.pactoLayoutSDKWrapper.sessionService) {
				this.isUsuarioPactoSolucoes =
					this.pactoLayoutSDKWrapper.sessionService.isUsuarioPactoSolucoes;
			}
		} else {
			this.configuracao = new PlataformaMenuV2Config();
		}
	}

	closeHandler() {
		this.cd.detectChanges();
	}

	closeLogoutHandler() {
		if (this.pactoLayoutSDKWrapper) {
			if (this.pactoLayoutSDKWrapper.sessionService) {
				this.pactoLayoutSDKWrapper.logout();
			}
		}
	}

	get colaboradorAvatar() {
		if (this.configuracao && this.configuracao.colaboradorAvatarUrl) {
			return this.configuracao.colaboradorAvatarUrl;
		} else {
			return null;
		}
	}

	redirectToCanalCliente() {
		this.router.navigate([PlataformModuleConfig.CCL.route.internalLink]);
	}

	redirectTreinoHome() {
		this.router.navigate(["treino", "home", { cb: true }]);
	}

	isString(colaboradorUrl: string | undefined) {
		return typeof colaboradorUrl === "string";
	}

	redirectToEditProfileLegado() {
		if (this.configuracao && this.configuracao.independente) {
			let usuarioId = null;

			if (this.configuracao.colaboradorUrl) {
				const urlParts = this.configuracao.colaboradorUrl.split("/");
				usuarioId = urlParts[urlParts.length - 1];
			}

			if (!usuarioId && this.pactoLayoutSDKWrapper) {
				usuarioId = this.pactoLayoutSDKWrapper.oamdUserId();
			}

			if (usuarioId) {
				this.router.navigate(["colaboradores", "user", usuarioId]);
				return;
			}
		}

		// Comportamento original para casos não treino independente
		const configProfileRedirect: PlatformMenuItem = {
			id: "user-profile",
			route: {
				queryParams: {
					openAsPopup: true,
					funcionalidadeNome: "DADOS_USUSARIO",
					windowTitle: "Dados Cadastrais",
					windowWidth: 820,
					windowHeight: 600,
				},
			},
			module: PlataformModuleConfig.ADM_LEGADO,
		};
		this.layoutNavigationService.makeRedirect(
			this.layoutNavigationService.redirectToModule(
				PlataformModuleConfig.ADM_LEGADO,
				configProfileRedirect
			) as Observable<string>
		);
	}

	toLower(str: string) {
		return str ? str.toLowerCase() : "";
	}

	trateAvatarError(event: ErrorEvent) {
		if (event.type === "error") {
			const target: HTMLImageElement = event.target as HTMLImageElement;
			target.src = "pacto-ui/images/user-image-default.svg";
			target.style.border = "unset";
		}
	}

	isExibirClubeDeBeneficios(): boolean {
		if (this.pactoLayoutSDKWrapper.sessionService) {
			return (
				this.pactoLayoutSDKWrapper.sessionService.clubeDeBeneficiosMarketing &&
				this.pactoLayoutSDKWrapper.sessionService.clubeDeBeneficiosMarketing
					.link
			);
		}
		return false;
	}

	isPossuiIntegracaoZW(): boolean {
		if (this.pactoLayoutSDKWrapper.sessionService) {
			return this.pactoLayoutSDKWrapper.sessionService.integracaoZW;
		}
		return false;
	}
}
