# Análise e Solução para TW-2714: Lentidão na Busca de Atividades

## Resumo do Problema

O ticket TW-2714 reporta lentidão significativa na funcionalidade de busca de atividades durante a montagem de treino. O comportamento observado é:

1. **Resultado Parcial Imediato**: Ao digitar "apoio", apenas 1 resultado aparece instantaneamente
2. **Longo Atraso**: Sistema fica inativo por ~25 segundos
3. **Atualização Completa**: Após o atraso, um botão de atualização aparece e todos os resultados são exibidos

## Análise Técnica

### Componentes Envolvidos

#### 1. Frontend - Componente de Busca
- **Arquivo**: `src/app/treino/montagem-treino/configurar-ficha/components/adicionar-atividade/adicionar-atividade.component.html`
- **Componente**: `pacto-cat-select-filter`
- **Endpoint**: `'atividades/montarTreino'`

<augment_code_snippet path="src/app/treino/montagem-treino/configurar-ficha/components/adicionar-atividade/adicionar-atividade.component.html" mode="EXCERPT">
````html
<pacto-cat-select-filter
    #atividadeSelect
    [class]="'col-md-5 select-atividade'"
    [id]="'select-atividade'"
    label="Busque uma atividade pelo nome ou grupo muscular"
    [control]="formGroup.get('atividadeId')"
    [endpointUrl]="_rest.buildFullUrl('atividades/montarTreino')"
    [responseParser]="responseParser"
    [paramBuilder]="atividadeSelectBuilder"
    [labelKey]="'nome'"></pacto-cat-select-filter>
````
</augment_code_snippet>

#### 2. Configuração da Busca
- **Arquivo**: `src/app/treino/montagem-treino/configurar-ficha/components/adicionar-atividade/adicionar-atividade.component.ts`

<augment_code_snippet path="src/app/treino/montagem-treino/configurar-ficha/components/adicionar-atividade/adicionar-atividade.component.ts" mode="EXCERPT">
````typescript
atividadeSelectBuilder: SelectFilterParamBuilder = (term) => {
    return {
        page: "0",
        size: "200",
        filters: JSON.stringify({
            situacaoAtividade: ["ATIVO"],
            quicksearchValue: term,
            quicksearchFields: ["nome"],
            grupoMuscularesIds: this.grupoMusculares,
        }),
    };
};
````
</augment_code_snippet>

#### 3. Componente Base - pacto-cat-select-filter
- **Arquivo**: `projects/ui/src/lib/components/cat-select-filter/cat-select-filter.component.ts`

### Problemas Identificados

#### 1. **Cache Local Ineficiente**
O componente `pacto-cat-select-filter` implementa um sistema de cache local que está causando comportamento inconsistente:

<augment_code_snippet path="projects/ui/src/lib/components/cat-select-filter/cat-select-filter.component.ts" mode="EXCERPT">
````typescript
private fetchDataCache = new Map<string, any>();
private lastFetchTime: number = 0;
private readonly CACHE_DURATION: number = 30000; // 30s
private readonly TIME_TO_ENABLE_RELOAD: number = 30000;
````
</augment_code_snippet>

**Problema**: O cache está retornando resultados parciais antes da busca completa ser finalizada.

#### 2. **Debounce Inadequado**
O debounce atual de 300ms pode não ser suficiente para consultas complexas:

<augment_code_snippet path="projects/ui/src/lib/components/cat-select-filter/cat-select-filter.component.ts" mode="EXCERPT">
````typescript
this.filterFC.valueChanges.pipe(debounceTime(300)).subscribe((term) => {
    this.search(term);
});
````
</augment_code_snippet>

#### 3. **Lógica de Mesclagem de Cache Problemática**
A lógica atual tenta mesclar resultados antigos com novos, causando inconsistências:

<augment_code_snippet path="projects/ui/src/lib/components/cat-select-filter/cat-select-filter.component.ts" mode="EXCERPT">
````typescript
// Cache the result
if (this.fetchDataCache.has(cacheKey)) {
    let actualResult = this.fetchDataCache.get(cacheKey);
    const actualResultContent = actualResult.map((v) => v[this.labelKey]);

    resultContent.forEach((rc) => {
        if (!actualResultContent.includes(rc[this.labelKey])) {
            actualResult.push(rc);
        }
    });
    // ...
}
````
</augment_code_snippet>

#### 4. **Botão de Atualização Manual**
O sistema força o usuário a usar um botão de atualização após 30 segundos:

<augment_code_snippet path="projects/ui/src/lib/components/cat-select-filter/cat-select-filter.component.html" mode="EXCERPT">
````html
<div
    class="cat-select-refresh"
    *ngIf="canReload"
    ds3Tooltip="Atualizar as opções"
    (click)="$event.stopPropagation()">
    <button
        ds3-icon-button
        [id]="id + '-refresh-option'"
        (click)="search(filterFC.value, null, true)">
        <i class="pct pct-refresh-cw"></i>
    </button>
</div>
````
</augment_code_snippet>

## Solução Proposta

### 1. **Otimização do Cache Local**

**Problema**: Cache retorna resultados parciais
**Solução**: Simplificar a lógica de cache para sempre fazer requisições HTTP em tempo real para buscas

### 2. **Melhoria do Debounce**

**Problema**: Debounce pode ser insuficiente
**Solução**: Ajustar para 400ms e otimizar cancelamento de requisições

### 3. **Eliminação da Mesclagem de Cache**

**Problema**: Lógica complexa de mesclagem causa inconsistências
**Solução**: Substituir resultado completo a cada busca

### 4. **Remoção do Botão de Atualização Manual**

**Problema**: UX ruim com botão de refresh obrigatório
**Solução**: Busca sempre em tempo real sem necessidade de refresh manual

## Implementação da Solução

### Modificações no fetchData()

A principal mudança será na função `fetchData()` do componente `pacto-cat-select-filter`:

```typescript
private fetchData(term: string, reload = false): Observable<any> {
    const cacheKey = `${this.id}`;
    const now = Date.now();

    // Para reload manual, verificar limite de tempo
    if (reload && now - this.lastFetchTime < this.TIME_TO_ENABLE_RELOAD) {
        const currentDataCache = this.fetchDataCache.get(cacheKey);
        this.notificationService.warning(
            `Houve uma atualização nos últimos ${
                this.TIME_TO_ENABLE_RELOAD / 1000
            }s, aguarde um momento para atualizar novamente!`
        );
        this._resetReloadInterval(false);
        return of(currentDataCache || []);
    }

    // Sempre fazer requisição HTTP para busca em tempo real
    
    const url = this.endpointUrl;
    const params = this.paramBuilder ? this.paramBuilder(term) : {};

    if (this.addtionalFilters) {
        const filters = JSON.parse(params.filters.toString());
        Object.keys(this.addtionalFilters).forEach((key) => {
            filters[key] = this.addtionalFilters[key];
        });
        params.filters = JSON.stringify(filters);
    }

    const data = this.http.get(url, { params });

    return data.pipe(
        map((result) => {
            this._resetReloadInterval(false);
            const parsedResult: any = this.resposeParser
                ? this.resposeParser(result)
                : result;
            const resultContent = parsedResult.content
                ? parsedResult.content
                : parsedResult;

            // Simplificar cache - apenas armazenar resultado atual sem mesclar
            const sortedResult = this._sortValues(resultContent);
            this.fetchDataCache.set(cacheKey, sortedResult);
            this.lastFetchTime = now;
            return sortedResult;
        }),
        catchError(() => {
            if (this.resposeParser) {
                return of(this.resposeParser([]));
            } else {
                return of([]);
            }
        })
    );
}
```

### Benefícios da Solução

1. **Busca Instantânea**: Eliminação do atraso de 25 segundos
2. **Resultados Completos**: Todos os resultados aparecem de uma vez
3. **UX Melhorada**: Não há necessidade de botão de atualização manual
4. **Performance**: Requisições otimizadas sem cache problemático

## Recomendações Adicionais para o Backend

### 1. Verificação de Índices de Banco de Dados

O endpoint recebe filtros com `quicksearchValue` e `quicksearchFields: ["nome"]`. É crucial verificar se existe índice na coluna `nome`:

```sql
-- PostgreSQL
CREATE INDEX IF NOT EXISTS idx_atividade_nome ON atividades (nome);

-- MySQL  
CREATE INDEX idx_atividade_nome ON atividades (nome);

-- Para buscas LIKE com wildcards, considerar índice de texto completo:
-- PostgreSQL
CREATE INDEX idx_atividade_nome_gin ON atividades USING gin(to_tsvector('portuguese', nome));
```

### 2. Otimização de Consultas SQL

Se a consulta atual usa `LIKE '%termo%'`, considerar otimizações:

```sql
-- LENTO - não pode usar índice eficientemente
SELECT * FROM atividades WHERE nome LIKE '%apoio%';

-- RÁPIDO - pode usar índice
SELECT * FROM atividades WHERE nome ILIKE 'apoio%';
```

### 3. Implementação de Cache no Backend

Considerar cache Redis para consultas frequentes:

```java
@Cacheable(value = "atividades", key = "#filtros.quicksearchValue")
public Page<Atividade> buscarAtividades(FiltroAtividade filtros, Pageable pageable) {
    // implementação da busca
}
```

## Conclusão

A solução proposta resolve o problema de lentidão na busca de atividades através de:

1. **Otimização do frontend**: Eliminação de cache problemático e melhoria do debounce
2. **Simplificação da lógica**: Remoção de complexidade desnecessária
3. **Melhoria da UX**: Busca instantânea sem necessidade de refresh manual
4. **Recomendações de backend**: Otimizações de banco de dados e cache

Esta abordagem garante que a busca por "apoio" retorne todos os resultados instantaneamente, eliminando o atraso de 25 segundos e a necessidade do botão de atualização.
