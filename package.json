{"name": "admin-angular", "version": "1.1.2189", "license": "MIT", "bin": {"setup-env": "docker/bin/setup-env.js"}, "scripts": {"prepare": "husky install", "config": "node setup-env.js", "setup": "node setup-env.js", "build": "npm-run-all --print-label build-libs build-apps", "build-all": "npm-run-all --print-label build-libs build-apps build-apps-en build-apps-es", "build-all-apps": "npm-run-all --parallel --print-label build-apps-en build-apps-es build-apps", "build-en": "npm-run-all --print-label build-libs build-apps-en", "build-es": "npm-run-all --print-label build-libs build-apps-es", "build-apps": "npm-run-all --print-label --parallel build-treino build-adm build-crm build-canal-cliente build-login", "build-apps-en": "npm-run-all --print-label --parallel build-treino-en build-adm-en build-crm-en build-login-en", "build-apps-es": "npm-run-all --print-label --parallel build-treino-es build-adm-es build-crm-es build-login-es", "build-libs": "npm-run-all --print-label build-sdk build-ui build-adapters build-pacto-layout", "build-libs-login": "npm-run-all --print-label build-sdk build-ui-kit build-adt-login-app-api", "build-libs-no-parallel": "npm-run-all --print-label build-sdk build-ui build-adapters-no-parallel build-pacto-layout", "build-pacto-layout": "ng b pacto-layout", "start": "node --max-old-space-size=4096 ./node_modules/@angular/cli/bin/ng serve --servePath=pt --base-href /pt/ --port=8000", "start-docker": "node --max-old-space-size=4096 ./node_modules/@angular/cli/bin/ng s --servePath=pt --base-href /pt/ --port=8000 --host host.docker.internal", "start-es": "ng s --servePath=es --base-href /es/ --port=8000", "build-canal-cliente": "ng b canal-cliente --base-href /pt/ --prod --delete-output-path --deploy-url './'", "build-marketing-all": "npm-run-all --parallel \"build-marketing -- --base-href {1}/pt/\" --", "build-marketing": "ng b marketing --base-href / --prod --delete-output-path --deploy-url './'", "build-treino-all": "npm-run-all --parallel \"build-treino -- --base-href {1}/pt/\" \"build-treino-es -- --base-href {1}/es/\" \"build-treino-en -- --base-href {1}/en/\" --", "build-treino": "ng b --base-href /pt/ --prod --delete-output-path --deploy-url './'", "build-treino-en": "ng b --base-href /en/ --prod -c en --delete-output-path --deploy-url './'", "build-treino-es": "ng b --base-href /es/ --prod -c es --delete-output-path --deploy-url './'", "build-login-all": "npm-run-all --parallel \"build-login -- --base-href {1}/pt/\" \"build-login-es -- --base-href {1}/es/\" \"build-login-en -- --base-href {1}/en/\" --", "build-login": "ng b login --base-href /pt/ --prod --delete-output-path --deploy-url './'", "build-login-en": "ng b login --base-href /en/ --prod -c en --delete-output-path --deploy-url './'", "build-login-es": "ng b login --base-href /es/ --prod -c es --delete-output-path --deploy-url './'", "build-adm-all": "npm-run-all --parallel \"build-adm -- --base-href {1}/pt/\" \"build-adm-es -- --base-href {1}/es/\" \"build-adm-en -- --base-href {1}/en/\" --", "build-adm": "ng b adm --base-href /pt/ --prod --delete-output-path --deploy-url './'", "build-adm-en": "ng b adm --base-href /en/ --prod -c en --delete-output-path --deploy-url './'", "build-adm-es": "ng b adm --base-href /es/ --prod -c es --delete-output-path --deploy-url './'", "build-crm-all": "npm-run-all --parallel \"build-crm -- --base-href {1}/pt/\" \"build-crm-es -- --base-href {1}/es/\" \"build-crm-en -- --base-href {1}/en/\" --", "build-crm": "ng b crm --base-href /pt/ --prod --delete-output-path --deploy-url './'", "build-crm-en": "ng b crm --base-href /en/ --prod -c en --delete-output-path --deploy-url './'", "build-crm-es": "ng b crm --base-href /es/ --prod -c es --delete-output-path --deploy-url './'", "build-nichos": "ng b nichos --base-href / --prod --delete-output-path --deploy-url './'", "copy-assets": "node ./projects/ui/copy-assets.js", "build-ui": "ng b ui && node ./projects/ui/copy-assets.js && ng b old-ui-kit", "build-ui-kit": "ng b ui && node ./projects/ui/copy-assets.js", "start-ui-sandbox": "ng s sandbox", "ui-watch": "ng b ui --watch", "watch-ui": "ng b ui --watch", "pacto-layout-watch": "ng b pacto-layout --watch", "watch-pacto-layout": "ng b pacto-layout --watch", "build-sdk": "ng b sdk", "start-adm": "ng s adm --base-href /pt/ --servePath=pt --port 8003", "start-adm-docker": "ng s adm --watch --base-href /pt/ --servePath=pt --port 8003 --host host.docker.internal --watch", "start-adm-es": "ng s adm -c es --servePath=es --base-href /es/ --port 8003", "start-adm-en": "ng s adm -c en --servePath=en --base-href /en/ --port 8003", "start-feature-docker": "ng s nichos --watch --base-href /pt/ --servePath=pt --port 8004 --host host.docker.internal --watch", "start-crm": "ng s crm --base-href /pt/ --servePath pt --port 8005", "start-marketing": "ng s marketing --servePath=marketing --base-href /marketing/ --port=8006", "start-nichos-docker": "ng s nichos --servePath=nichos --base-href /nichos/ --port=8007 --host host.docker.internal", "start-nichos": "ng s nichos --servePath=nichos --base-href /nichos/ --port=8007", "build-acesso-sistema-api-watch": "ng b acesso-sistema-api --watch", "watch-clube-vantagens-api": "ng b clube-vantagens-api --watch", "build-plano-api-watch": "ng b plano-api --watch", "login-app-api-watch": "ng b login-app-api --watch", "build-login-app-api": "ng b login-app-api", "start-login": "ng s login --base-href /pt/ --servePath=pt --port 8004", "start-login-docker": "ng s login --base-href /pt/ --servePath=pt --port 8004 --host host.docker.internal", "start-login-en": "ng s login -c en --base-href /en/ --servePath=en --port 8004", "start-login-es": "ng s login -c es --base-href /es/ --servePath=es --port 8004", "ng": "./node_modules/@angular/cli/bin/ng", "lint": "ng lint plataforma-pacto --fix", "extract-i18n": "ng xi18n plataforma-pacto --i18n-format xlf --output-path i18n --i18n-locale pt && ng run plataforma-pacto:xliffmerge", "extract-i18n-adm": "ng xi18n adm --i18n-format xlf --output-path i18n --i18n-locale pt && ng run adm:xliffmerge", "extract-i18n-login": "ng xi18n login --i18n-format xlf --output-path i18n --i18n-locale pt && ng run login:xliffmerge", "extract-i18n-crm": "ng xi18n crm --i18n-format xlf --output-path i18n --i18n-locale pt && ng run crm:xliffmerge", "build-adapters": "npm-run-all --print-label --parallel build-adt-acesso-sistema-api build-adt-adm-core-api build-adt-adm-legado-api build-adt-adm-ms-api build-adt-cadastro-aux-api build-adt-clube-vantagens-api build-adt-crm-api build-adt-login-app-api build-adt-midia-social-api build-adt-ms-pactopay-api build-adt-pacto-api build-adt-pactopay-api build-adt-plano-api build-adt-produto-api build-adt-relatorio-api build-adt-treino-api build-adt-zw-pactopay-api build-adt-marketing-api build-adt-integracao-gympass-api build-adt-notificacao-api build-adt-zw-servlet-api build-adt-pessoa-ms-api build-adt-financeiro-api build-adt-autenticacao-api build-adt-recurso-ms-api build-adt-bi-ms-api", "build-adapters-no-parallel": "npm-run-all --print-label build-adt-acesso-sistema-api build-adt-adm-core-api build-adt-adm-legado-api build-adt-adm-ms-api build-adt-cadastro-aux-api build-adt-clube-vantagens-api build-adt-crm-api build-adt-login-app-api build-adt-midia-social-api build-adt-ms-pactopay-api build-adt-pacto-api build-adt-pactopay-api build-adt-plano-api build-adt-produto-api build-adt-relatorio-api build-adt-treino-api build-adt-zw-pactopay-api build-adt-marketing-api build-adt-integracao-gympass-api build-adt-notificacao-api build-adt-zw-servlet-api build-adt-pessoa-ms-api build-adt-financeiro-api build-adt-autenticacao-api build-adt-recurso-ms-api build-adt-bi-ms-api", "build-adt-acesso-sistema-api": "ng build acesso-sistema-api", "build-adt-bi-ms-api": "ng build bi-ms-api", "build-adt-cadastro-aux-api": "ng build cadastro-aux-api", "build-adt-plano-api": "ng build plano-api", "build-adt-produto-api": "ng build produto-api", "build-adt-adm-core-api": "ng build adm-core-api", "build-adt-adm-legado-api": "ng build adm-legado-api", "build-adt-adm-ms-api": "ng build adm-ms-api", "build-adt-notificacao-api": "ng build notificacao-api", "build-adt-notificacao-api-watch": "ng build notificacao-api --watch", "build-adt-relatorio-api": "ng build relatorio-api", "build-adt-pacto-api": "ng build pacto-api", "build-adt-zw-pactopay-api": "ng build zw-pactopay-api", "build-adt-ms-pactopay-api": "ng build ms-pactopay-api", "build-adt-treino-api": "ng build treino-api", "build-adt-integracao-gympass-api": "ng build integracao-gympass-api", "build-adt-clube-vantagens-api": "ng build clube-vantagens-api", "build-adt-crm-api": "ng build crm-api", "build-adt-login-app-api": "ng build login-app-api", "build-adt-midia-social-api": "ng build midia-social-api", "build-adt-pactopay-api": "ng build pactopay-api", "build-adt-zw-servlet-api": "ng build zw-servlet-api", "build-adt-marketing-api": "ng build marketing-api", "build-adt-financeiro-api": "ng build financeiro-ms-api", "build-adt-pessoa-ms-api": "ng build pessoa-ms-api", "build-adt-recurso-ms-api": "ng build recurso-ms-api", "build-adt-autenticacao-api": "ng build autenticacao-api", "docs:json": "compodoc -p ./tsconfig.json -e json -d .", "storybook": "npm run docs:json && start-storybook -p 6006", "start-storybook": "start-storybook -p 6006 --ci", "build-storybook": "build-storybook", "husky:install": "husky install", "lint-staged": "lint-staged"}, "private": true, "dependencies": {"stylus": "git+https://github.com/stylus/stylus.git#0.54.5", "@amcharts/amcharts3-angular": "^2.2.4", "@angular-devkit/build-angular": "~0.803.25", "@angular-devkit/build-ng-packagr": "~0.803.25", "@angular/animations": "8.2.14", "@angular/cdk": "~7.3.7", "@angular/cli": "^8.3.26", "@angular/common": "^8.2.14", "@angular/compiler": "^8.2.14", "@angular/compiler-cli": "^8.2.14", "@angular/core": "^8.2.14", "@angular/forms": "^8.2.14", "@angular/language-service": "^8.2.14", "@angular/material": "^7.3.7", "@angular/platform-browser": "^8.2.14", "@angular/platform-browser-dynamic": "^8.2.14", "@angular/router": "^8.2.14", "@auth0/angular-jwt": "^2.1.2", "@babel/core": "^7.6.2", "@ng-bootstrap/ng-bootstrap": "4.1.1", "@ngx-i18nsupport/tooling": "^8.0.3", "@ngx-translate/core": "^12.0.0", "@storybook/addon-knobs": "^6.4.0", "@storybook/cli": "^5.2.1", "@types/node": "^6.0.118", "angular-password-strength-meter": "^2.0.0", "angular2-text-mask": "^9.0.0", "apexcharts": "3.49.1", "argparse": "^1.0.10", "autoprefixer": "9.6.1", "babel-loader": "^8.0.6", "bootstrap": "4.1.1", "canvas-confetti": "^1.5.1", "chalk": "^4", "codelyzer": "^5.0.0", "core-js": "^2.6.9", "credit-card-type": "^8.2.0", "crypto-js": "^3.1.2", "firebase": "^6.6.0", "fuzzy-search": "^3.2.1", "hammerjs": "^2.0.8", "inquirer": "^8.2.5", "jquery": "3.2.1", "moment": "^2.24.0", "mousetrap": "^1.6.3", "ncp": "^2.0.0", "ng-apexcharts": "1.5.6", "ng-click-outside": "^8.0.0", "ng-packagr": "^5.4.0", "ng-recaptcha": "^6.1.0", "ng-simple-slideshow": "^1.2.9", "ng-snotify": "^4.3.1", "ng2-dragula": "^2.1.1", "ng2-search-filter": "0.4.7", "ngx-cookie-service": "^2.4.0", "ngx-currency": "^2.5.2", "ngx-image-compress-legacy": "^12.0.0", "ngx-infinite-scroll": "8.0.2", "ngx-mask": "^8.2.0", "ngx-material-timepicker": "^5.6.0", "ngx-quill": "6.0.0", "ngx-toastr": "11.3.3", "node-sass": "^4.12.0", "npm-run-all": "^4.1.5", "pretty-json-stringify": "0.0.2", "quill": "^1.3.7", "quill-blot-formatter": "^1.0.5", "resize-observer-polyfill": "^1.5.1", "rxjs": "^6.5.3", "sass-loader": "^7.3.1", "saturn-datepicker": "^8.0.5", "smooth-scrollbar": "^8.4.0", "text-mask-addons": "^3.8.0", "ts-node": "~3.2.0", "tsickle": "^0.37.0", "tslib": "^1.10.0", "tslint": "~5.15.0", "typescript": "3.5.3", "typescript-require": "0.2.10", "zone.js": "~0.9.1", "zxcvbn": "^4.4.2"}, "devDependencies": {"@angular-devkit/build-angular": "~0.803.29", "@angular-devkit/build-ng-packagr": "~0.803.29", "@angular/compiler-cli": "~8.2.14", "@compodoc/compodoc": "^1.1.19", "@ngx-i18nsupport/ngx-i18nsupport": "^1.1.6", "@storybook/addon-actions": "^5.2.1", "@storybook/addon-docs": "^5.3.21", "@storybook/addon-links": "^5.2.1", "@storybook/addon-notes": "^5.2.1", "@storybook/addon-storysource": "^5.3.21", "@storybook/addons": "^5.2.1", "@storybook/angular": "^5.2.1", "@types/jasmine": "~2.8.8", "@types/jasminewd2": "^2.0.6", "cypress": "3.4.0", "data-store": "^4.0.3", "husky": "^7.0.4", "jasmine-core": "~2.99.1", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.0.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "^2.0.6", "karma-jasmine": "~1.1.2", "karma-jasmine-html-reporter": "^0.2.2", "lint-staged": "^12.5.0", "ng-packagr": "^5.4.0", "prettier": "^2.8.8", "prettier-plugin-organize-imports": "^4.0.0", "protractor": "~5.4.0", "tsickle": ">=0.34.0", "tslib": "^1.10.0", "typescript": "~3.5.3"}, "resolutions": {"stylus": "github:stylus/stylus#0.54.5"}}